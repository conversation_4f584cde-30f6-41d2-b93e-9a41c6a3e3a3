/**
 * API client for OneOptimizer backend
 */

import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for API responses
export interface Algorithm {
  name: string;
  description: string;
  capabilities: string[];
  input_requirements: string[];
  output_format: string;
}

export interface AlgorithmCategory {
  [key: string]: Algorithm;
}

export interface AlgorithmsResponse {
  success: boolean;
  algorithms: {
    econometric_models: AlgorithmCategory;
    financial_analysis: AlgorithmCategory;
    ai_agents: AlgorithmCategory;
  };
  total_count: number;
  categories: string[];
}

export interface AnalysisRequest {
  data: any;
  context?: string;
  periods?: number;
  method?: 'econometric' | 'ai' | 'hybrid';
}

export interface AnalysisResponse {
  success: boolean;
  analysis?: any;
  forecast?: any;
  data?: any;
  algorithm: string;
  timestamp: string;
  message?: string;
}

// API functions
export const apiClient = {
  // Get available algorithms
  async getAlgorithms(): Promise<AlgorithmsResponse> {
    const response = await api.get('/api/algorithms');
    return response.data;
  },

  // Health check
  async healthCheck() {
    const response = await api.get('/api/health');
    return response.data;
  },

  // Time series analysis
  async analyzeTimeSeries(request: AnalysisRequest): Promise<AnalysisResponse> {
    const response = await api.post('/api/analysis/time-series', request);
    return response.data;
  },

  // Forecasting
  async createForecast(request: AnalysisRequest): Promise<AnalysisResponse> {
    const response = await api.post('/api/analysis/forecast', request);
    return response.data;
  },

  // Financial ratios analysis
  async analyzeFinancialRatios(request: {
    financial_data: any;
    industry_type?: string;
  }): Promise<AnalysisResponse> {
    const response = await api.post('/api/analysis/financial-ratios', request);
    return response.data;
  },

  // AI Agents
  async runRevenueAgent(request: any): Promise<AnalysisResponse> {
    const response = await api.post('/api/agents/revenue', request);
    return response.data;
  },

  async runScenarioAgent(request: any): Promise<AnalysisResponse> {
    const response = await api.post('/api/agents/scenario', request);
    return response.data;
  },

  // Direct Monte Carlo simulation - pure math, no AI overhead
  async runMonteCarloSimulation(request: any): Promise<AnalysisResponse> {
    const response = await api.post('/api/monte-carlo', request);
    return response.data;
  },

  // Wizard processes
  async runWizardProcess(request: {
    process_type: string;
    [key: string]: any;
  }): Promise<AnalysisResponse> {
    const response = await api.post('/api/wizard/process', request);
    return response.data;
  },

  // Data management
  async uploadDataFile(file: File, dataType: string, description?: string): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data_type', dataType);
    if (description) {
      formData.append('description', description);
    }

    const response = await api.post('/api/data/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async listUploadedData(): Promise<any> {
    const response = await api.get('/api/data/list');
    return response.data;
  },

  // Macro economics
  async getMacroIndicators(params: {
    countries?: string;
    indicators?: string;
    years?: string;
  }): Promise<any> {
    const response = await api.get('/api/macro/indicators', { params });
    return response.data;
  },

  async getAvailableCountries(): Promise<any> {
    const response = await api.get('/api/macro/countries');
    return response.data;
  },

  async getAvailableIndicators(): Promise<any> {
    const response = await api.get('/api/macro/indicators/list');
    return response.data;
  },

  async analyzeMacroData(request: {
    countries: string[];
    business_context?: string;
    analysis_type?: string;
  }): Promise<any> {
    const response = await api.post('/api/macro/analysis', request);
    return response.data;
  },
};

// Error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    throw error;
  }
);

export default apiClient;
