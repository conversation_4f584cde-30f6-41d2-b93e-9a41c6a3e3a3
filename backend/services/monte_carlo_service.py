"""
Monte Carlo Simulation Service for OneOptimizer.

Pure mathematical Monte Carlo simulations without AI overhead.
Fast, reliable, and cost-effective statistical calculations.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SimulationParameters:
    """Parameters for Monte Carlo simulation."""
    base_opex: float
    base_capex: float
    base_revenue: float
    revenue_growth_mean: float = 0.05
    revenue_growth_std: float = 0.02
    cost_inflation_mean: float = 0.03
    cost_inflation_std: float = 0.01
    fx_volatility: float = 0.05
    opex_limit: Optional[float] = None
    capex_limit: Optional[float] = None
    n_simulations: int = 1000
    confidence_levels: List[float] = None
    
    def __post_init__(self):
        if self.confidence_levels is None:
            self.confidence_levels = [0.95, 0.90, 0.75]


@dataclass
class SimulationResults:
    """Results from Monte Carlo simulation."""
    simulation_data: List[Dict[str, float]]
    summary_stats: Dict[str, float]
    confidence_intervals: Dict[str, Dict[str, float]]
    risk_metrics: Dict[str, float]
    metadata: Dict[str, Any]


class MonteCarloService:
    """
    Pure mathematical Monte Carlo simulation service.
    
    Features:
    - Fast numerical simulations using NumPy
    - Statistical analysis and confidence intervals
    - Risk metrics calculation
    - No AI overhead - pure mathematics
    """
    
    def __init__(self):
        """Initialize the Monte Carlo service."""
        self.logger = logging.getLogger(__name__)
    
    def run_simulation(
        self, 
        uploaded_data: Dict[str, Any], 
        frontend_params: Dict[str, Any],
        n_simulations: int = 1000
    ) -> SimulationResults:
        """
        Run Monte Carlo simulation using uploaded data and frontend parameters.
        
        Args:
            uploaded_data: Data from uploaded OPEX/CAPEX files
            frontend_params: Parameters from UI (constraints, assumptions)
            n_simulations: Number of simulation runs
            
        Returns:
            SimulationResults with comprehensive analysis
        """
        try:
            # Extract base values from uploaded data
            base_values = self._extract_base_values(uploaded_data)
            
            # Create simulation parameters
            sim_params = self._create_simulation_parameters(
                base_values, frontend_params, n_simulations
            )
            
            # Run the simulation
            simulation_data = self._execute_simulation(sim_params)
            
            # Analyze results
            summary_stats = self._calculate_summary_statistics(simulation_data)
            confidence_intervals = self._calculate_confidence_intervals(
                simulation_data, sim_params.confidence_levels
            )
            risk_metrics = self._calculate_risk_metrics(simulation_data)
            
            # Create metadata
            metadata = {
                "simulation_count": n_simulations,
                "base_opex": sim_params.base_opex,
                "base_capex": sim_params.base_capex,
                "base_revenue": sim_params.base_revenue,
                "opex_files_used": len(uploaded_data.get("opex_data", [])),
                "capex_files_used": len(uploaded_data.get("capex_data", [])),
                "execution_time": datetime.now().isoformat(),
                "constraints_applied": {
                    "opex_limit": sim_params.opex_limit,
                    "capex_limit": sim_params.capex_limit
                }
            }
            
            return SimulationResults(
                simulation_data=simulation_data,
                summary_stats=summary_stats,
                confidence_intervals=confidence_intervals,
                risk_metrics=risk_metrics,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Monte Carlo simulation failed: {str(e)}")
            raise
    
    def _extract_base_values(self, uploaded_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract base OPEX, CAPEX, and revenue from uploaded data."""
        base_opex = self._calculate_base_opex(uploaded_data.get("opex_data", []))
        base_capex = self._calculate_base_capex(uploaded_data.get("capex_data", []))
        base_revenue = base_opex * 1.25  # Assume 25% margin over OPEX
        
        return {
            "base_opex": base_opex,
            "base_capex": base_capex,
            "base_revenue": base_revenue
        }
    
    def _calculate_base_opex(self, opex_data: List[Dict[str, Any]]) -> float:
        """Calculate base OPEX from uploaded data."""
        if not opex_data:
            return 50_000_000  # Default $50M
        
        total_opex = 0
        for opex_file in opex_data:
            try:
                data = opex_file.get("data", [])
                if isinstance(data, list):
                    for record in data:
                        if isinstance(record, dict):
                            # Try different column names for cost amounts
                            for col in ["amount", "cost", "value", "total", "expense"]:
                                if col in record and isinstance(record[col], (int, float)):
                                    total_opex += record[col]
                                    break
            except Exception as e:
                self.logger.warning(f"Error processing OPEX file: {str(e)}")
                continue
        
        return total_opex if total_opex > 0 else 50_000_000
    
    def _calculate_base_capex(self, capex_data: List[Dict[str, Any]]) -> float:
        """Calculate base CAPEX from uploaded data."""
        if not capex_data:
            return 25_000_000  # Default $25M
        
        total_capex = 0
        for capex_file in capex_data:
            try:
                data = capex_file.get("data", {})
                if isinstance(data, dict) and "projects" in data:
                    # Handle JSON structure with projects
                    projects = data["projects"]
                    for project in projects:
                        if isinstance(project, dict) and "total_amount" in project:
                            total_capex += project["total_amount"]
                elif isinstance(data, list):
                    # Handle tabular data
                    for record in data:
                        if isinstance(record, dict):
                            for col in ["total_amount", "amount", "investment", "capex", "value"]:
                                if col in record and isinstance(record[col], (int, float)):
                                    total_capex += record[col]
                                    break
            except Exception as e:
                self.logger.warning(f"Error processing CAPEX file: {str(e)}")
                continue
        
        return total_capex if total_capex > 0 else 25_000_000
    
    def _create_simulation_parameters(
        self, 
        base_values: Dict[str, float], 
        frontend_params: Dict[str, Any],
        n_simulations: int
    ) -> SimulationParameters:
        """Create simulation parameters from base values and frontend inputs."""
        base_assumptions = frontend_params.get("base_assumptions", {})
        constraints = frontend_params.get("constraints", {})
        risk_params = frontend_params.get("risk_parameters", {})
        
        # Map risk appetite to volatility
        risk_appetite = risk_params.get("risk_appetite", "medium")
        volatility_multiplier = {
            "low": 0.5,
            "medium": 1.0,
            "high": 1.5
        }.get(risk_appetite, 1.0)
        
        return SimulationParameters(
            base_opex=base_values["base_opex"],
            base_capex=base_values["base_capex"],
            base_revenue=base_values["base_revenue"],
            revenue_growth_mean=base_assumptions.get("revenue_growth", 0.05),
            revenue_growth_std=0.02 * volatility_multiplier,
            cost_inflation_mean=0.03,
            cost_inflation_std=0.01 * volatility_multiplier,
            fx_volatility=0.05 * volatility_multiplier,
            opex_limit=constraints.get("opex_limit"),
            capex_limit=constraints.get("capex_limit"),
            n_simulations=n_simulations
        )
    
    def _execute_simulation(self, params: SimulationParameters) -> List[Dict[str, float]]:
        """Execute the Monte Carlo simulation."""
        # Pre-generate all random samples for efficiency
        revenue_growth_samples = np.random.normal(
            params.revenue_growth_mean, 
            params.revenue_growth_std, 
            params.n_simulations
        )
        cost_inflation_samples = np.random.normal(
            params.cost_inflation_mean,
            params.cost_inflation_std,
            params.n_simulations
        )
        fx_impact_samples = np.random.normal(0, params.fx_volatility, params.n_simulations)
        
        simulation_data = []
        
        for i in range(params.n_simulations):
            # Sample parameters
            revenue_growth = revenue_growth_samples[i]
            cost_inflation = cost_inflation_samples[i]
            fx_impact = fx_impact_samples[i]
            
            # Calculate projected values
            projected_revenue = params.base_revenue * (1 + revenue_growth) * (1 + fx_impact)
            projected_opex = params.base_opex * (1 + cost_inflation)
            projected_capex = params.base_capex
            
            # Apply constraints
            if params.opex_limit:
                projected_opex = min(projected_opex, params.opex_limit)
            if params.capex_limit:
                projected_capex = min(projected_capex, params.capex_limit)
            
            # Calculate derived metrics
            total_costs = projected_opex + projected_capex
            net_income = projected_revenue - total_costs
            margin = net_income / projected_revenue if projected_revenue > 0 else 0
            roi = net_income / total_costs if total_costs > 0 else 0
            
            simulation_data.append({
                "simulation_id": i,
                "revenue": projected_revenue,
                "opex": projected_opex,
                "capex": projected_capex,
                "total_costs": total_costs,
                "net_income": net_income,
                "margin": margin,
                "roi": roi,
                "revenue_growth": revenue_growth,
                "cost_inflation": cost_inflation,
                "fx_impact": fx_impact
            })
        
        return simulation_data
    
    def _calculate_summary_statistics(self, simulation_data: List[Dict[str, float]]) -> Dict[str, float]:
        """Calculate summary statistics from simulation results."""
        df = pd.DataFrame(simulation_data)
        
        metrics = ["revenue", "opex", "capex", "net_income", "margin", "roi"]
        summary = {}
        
        for metric in metrics:
            if metric in df.columns:
                summary[f"{metric}_mean"] = float(df[metric].mean())
                summary[f"{metric}_std"] = float(df[metric].std())
                summary[f"{metric}_min"] = float(df[metric].min())
                summary[f"{metric}_max"] = float(df[metric].max())
                summary[f"{metric}_median"] = float(df[metric].median())
        
        return summary
    
    def _calculate_confidence_intervals(
        self, 
        simulation_data: List[Dict[str, float]], 
        confidence_levels: List[float]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate confidence intervals for key metrics."""
        df = pd.DataFrame(simulation_data)
        intervals = {}
        
        metrics = ["revenue", "net_income", "margin", "roi"]
        
        for metric in metrics:
            if metric in df.columns:
                intervals[metric] = {}
                for confidence in confidence_levels:
                    alpha = 1 - confidence
                    lower_percentile = (alpha / 2) * 100
                    upper_percentile = (1 - alpha / 2) * 100
                    
                    intervals[metric][f"ci_{int(confidence*100)}"] = {
                        "lower": float(np.percentile(df[metric], lower_percentile)),
                        "upper": float(np.percentile(df[metric], upper_percentile))
                    }
        
        return intervals
    
    def _calculate_risk_metrics(self, simulation_data: List[Dict[str, float]]) -> Dict[str, float]:
        """Calculate risk metrics from simulation results."""
        df = pd.DataFrame(simulation_data)
        
        # Value at Risk (VaR) for net income
        net_income_var_95 = float(np.percentile(df["net_income"], 5))
        net_income_var_99 = float(np.percentile(df["net_income"], 1))
        
        # Probability of loss
        prob_loss = float((df["net_income"] < 0).mean())
        
        # Probability of meeting targets (positive margin)
        prob_positive_margin = float((df["margin"] > 0).mean())
        
        # Downside deviation
        negative_returns = df["net_income"][df["net_income"] < df["net_income"].mean()]
        downside_deviation = float(negative_returns.std()) if len(negative_returns) > 0 else 0.0
        
        return {
            "value_at_risk_95": net_income_var_95,
            "value_at_risk_99": net_income_var_99,
            "probability_of_loss": prob_loss,
            "probability_positive_margin": prob_positive_margin,
            "downside_deviation": downside_deviation,
            "volatility": float(df["net_income"].std())
        }
