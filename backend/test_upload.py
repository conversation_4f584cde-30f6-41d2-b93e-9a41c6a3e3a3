#!/usr/bin/env python3
"""
Test script for file upload functionality.
Tests both XLSX and JSON file uploads.
"""

import requests
import json
import os
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_FILES_DIR = Path("../data/input")

def test_json_upload():
    """Test JSON file upload with CAPEX data."""
    print("🧪 Testing JSON file upload...")
    
    json_file_path = TEST_FILES_DIR / "capex_projects_2024.json"
    
    if not json_file_path.exists():
        print(f"❌ Test file not found: {json_file_path}")
        return False
    
    try:
        with open(json_file_path, 'rb') as f:
            files = {'file': ('capex_projects_2024.json', f, 'application/json')}
            data = {
                'data_type': 'capex',
                'description': 'Test CAPEX projects data upload'
            }
            
            response = requests.post(f"{API_BASE_URL}/api/data/upload", files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            print("✅ JSON upload successful!")
            print(f"   - File: {result['data']['filename']}")
            print(f"   - Rows: {result['data']['rows']}")
            print(f"   - Schema: {result['data'].get('schema_type', 'N/A')}")
            print(f"   - Analysis: {len(result['data']['initial_analysis'].get('insights', []))} insights")
            return True
        else:
            print(f"❌ JSON upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ JSON upload error: {str(e)}")
        return False

def test_excel_upload():
    """Test Excel file upload with OPEX data."""
    print("🧪 Testing Excel file upload...")
    
    excel_file_path = TEST_FILES_DIR / "fixed_costs_2024.xlsx"
    
    if not excel_file_path.exists():
        print(f"❌ Test file not found: {excel_file_path}")
        return False
    
    try:
        with open(excel_file_path, 'rb') as f:
            files = {'file': ('fixed_costs_2024.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            data = {
                'data_type': 'opex',
                'description': 'Test OPEX fixed costs data upload'
            }
            
            response = requests.post(f"{API_BASE_URL}/api/data/upload", files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            print("✅ Excel upload successful!")
            print(f"   - File: {result['data']['filename']}")
            print(f"   - Rows: {result['data']['rows']}")
            print(f"   - Columns: {result['data']['columns']}")
            print(f"   - Analysis: {len(result['data']['initial_analysis'].get('insights', []))} insights")
            return True
        else:
            print(f"❌ Excel upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Excel upload error: {str(e)}")
        return False

def test_api_health():
    """Test if the API is running."""
    print("🔍 Checking API health...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API not accessible: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting file upload tests...\n")
    
    # Check API health
    if not test_api_health():
        print("❌ Cannot proceed with tests - API is not accessible")
        return
    
    print()
    
    # Test JSON upload
    json_success = test_json_upload()
    print()
    
    # Test Excel upload
    excel_success = test_excel_upload()
    print()
    
    # Summary
    print("📊 Test Summary:")
    print(f"   - JSON upload: {'✅ PASS' if json_success else '❌ FAIL'}")
    print(f"   - Excel upload: {'✅ PASS' if excel_success else '❌ FAIL'}")
    
    if json_success and excel_success:
        print("\n🎉 All tests passed! File upload functionality is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
