"""
JSON File Processor for OneOptimizer.

This module handles processing of JSON files containing financial data
such as CAPEX projects, OPEX data, and revenue information.
"""

import json
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class JSONProcessor:
    """
    Processor for JSON files containing financial and business data.
    
    Handles various JSON structures including:
    - CAPEX project data with metadata
    - OPEX cost data
    - Revenue and financial data
    - Generic structured data
    """
    
    def __init__(self):
        """Initialize the JSON processor."""
        self.processed_files = {}
        self.supported_schemas = {
            'capex_projects': self._validate_capex_schema,
            'opex_data': self._validate_opex_schema,
            'revenue_data': self._validate_revenue_schema,
            'generic': self._validate_generic_schema
        }
    
    def process_json_file(self, file_path: Union[str, Path], schema_hint: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a JSON file and extract structured data.
        
        Args:
            file_path: Path to the JSON file
            schema_hint: Optional hint about the expected schema type
            
        Returns:
            Dictionary with processed data and metadata
        """
        try:
            file_path = Path(file_path)
            
            # Validate file
            validation_result = self._validate_file(file_path)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"],
                    "data": None
                }
            
            # Load JSON file
            load_result = self._load_json_file(file_path)
            if not load_result["success"]:
                return load_result
            
            json_data = load_result["data"]
            
            # Infer schema type
            schema_type = self._infer_schema_type(json_data, schema_hint)
            
            # Validate against schema
            schema_validation = self._validate_schema(json_data, schema_type)
            if not schema_validation["valid"]:
                logger.warning(f"Schema validation failed: {schema_validation['error']}")
            
            # Process data based on schema
            processed_result = self._process_by_schema(json_data, schema_type)
            
            # Store results
            file_key = str(file_path)
            self.processed_files[file_key] = {
                "processed_at": datetime.now().isoformat(),
                "schema_type": schema_type,
                "data": processed_result["data"],
                "metadata": processed_result["metadata"]
            }
            
            return {
                "success": True,
                "data": processed_result["data"],
                "schema_type": schema_type,
                "metadata": processed_result["metadata"],
                "file_info": {
                    "file_path": str(file_path),
                    "file_size": file_path.stat().st_size,
                    "processed_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"JSON processing error: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to process JSON file: {str(e)}",
                "data": None
            }
    
    def _validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate that the file exists and is readable."""
        if not file_path.exists():
            return {"valid": False, "error": "File does not exist"}
        
        if not file_path.is_file():
            return {"valid": False, "error": "Path is not a file"}
        
        if file_path.suffix.lower() != '.json':
            return {"valid": False, "error": "File is not a JSON file"}
        
        try:
            # Check if file is readable
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1)  # Try to read first character
            return {"valid": True}
        except Exception as e:
            return {"valid": False, "error": f"File is not readable: {str(e)}"}
    
    def _load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """Load and parse JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            return {
                "success": True,
                "data": json_data
            }
            
        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": f"Invalid JSON format: {str(e)}",
                "data": None
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to load JSON file: {str(e)}",
                "data": None
            }
    
    def _infer_schema_type(self, json_data: Any, schema_hint: Optional[str] = None) -> str:
        """Infer the schema type from JSON data structure."""
        if schema_hint and schema_hint in self.supported_schemas:
            return schema_hint
        
        # Check for CAPEX projects structure
        if isinstance(json_data, dict) and 'projects' in json_data:
            projects = json_data['projects']
            if isinstance(projects, list) and projects:
                # Check if projects have typical CAPEX fields
                sample_project = projects[0]
                capex_fields = ['total_amount', 'business_unit', 'category', 'start_date']
                if any(field in sample_project for field in capex_fields):
                    return 'capex_projects'
        
        # Check for OPEX data structure
        if isinstance(json_data, dict) and any(key in json_data for key in ['costs', 'expenses', 'opex']):
            return 'opex_data'
        
        # Check for revenue data structure
        if isinstance(json_data, dict) and any(key in json_data for key in ['revenue', 'sales', 'income']):
            return 'revenue_data'
        
        # Default to generic
        return 'generic'
    
    def _validate_schema(self, json_data: Any, schema_type: str) -> Dict[str, Any]:
        """Validate JSON data against expected schema."""
        if schema_type in self.supported_schemas:
            return self.supported_schemas[schema_type](json_data)
        else:
            return {"valid": True, "message": "No specific validation for this schema type"}
    
    def _validate_capex_schema(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate CAPEX projects schema."""
        if not isinstance(json_data, dict):
            return {"valid": False, "error": "CAPEX data must be a JSON object"}
        
        if 'projects' not in json_data:
            return {"valid": False, "error": "CAPEX data must contain 'projects' array"}
        
        projects = json_data['projects']
        if not isinstance(projects, list):
            return {"valid": False, "error": "'projects' must be an array"}
        
        # Validate project structure
        required_fields = ['name', 'total_amount']
        for i, project in enumerate(projects):
            if not isinstance(project, dict):
                return {"valid": False, "error": f"Project {i} must be an object"}
            
            for field in required_fields:
                if field not in project:
                    return {"valid": False, "error": f"Project {i} missing required field: {field}"}
        
        return {"valid": True, "message": "CAPEX schema validation passed"}
    
    def _validate_opex_schema(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate OPEX data schema."""
        # Basic validation for OPEX data
        return {"valid": True, "message": "OPEX schema validation passed"}
    
    def _validate_revenue_schema(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate revenue data schema."""
        # Basic validation for revenue data
        return {"valid": True, "message": "Revenue schema validation passed"}
    
    def _validate_generic_schema(self, json_data: Any) -> Dict[str, Any]:
        """Validate generic JSON data."""
        return {"valid": True, "message": "Generic schema validation passed"}
    
    def _process_by_schema(self, json_data: Any, schema_type: str) -> Dict[str, Any]:
        """Process JSON data based on identified schema type."""
        if schema_type == 'capex_projects':
            return self._process_capex_data(json_data)
        elif schema_type == 'opex_data':
            return self._process_opex_data(json_data)
        elif schema_type == 'revenue_data':
            return self._process_revenue_data(json_data)
        else:
            return self._process_generic_data(json_data)
    
    def _process_capex_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process CAPEX projects data."""
        projects = json_data.get('projects', [])
        metadata = json_data.get('metadata', {})
        
        # Extract key metrics
        total_investment = sum(p.get('total_amount', 0) for p in projects)
        business_units = list(set(p.get('business_unit', 'Unknown') for p in projects))
        categories = list(set(p.get('category', 'Unknown') for p in projects))
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(projects)
        
        processed_data = {
            "projects": projects,
            "summary": {
                "total_projects": len(projects),
                "total_investment": total_investment,
                "business_units": business_units,
                "categories": categories,
                "avg_project_size": total_investment / len(projects) if projects else 0
            },
            "dataframe": df
        }
        
        return {
            "data": processed_data,
            "metadata": {
                "original_metadata": metadata,
                "processing_info": {
                    "schema_type": "capex_projects",
                    "records_processed": len(projects),
                    "processing_timestamp": datetime.now().isoformat()
                }
            }
        }
    
    def _process_opex_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process OPEX data."""
        return {
            "data": json_data,
            "metadata": {
                "schema_type": "opex_data",
                "processing_timestamp": datetime.now().isoformat()
            }
        }
    
    def _process_revenue_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process revenue data."""
        return {
            "data": json_data,
            "metadata": {
                "schema_type": "revenue_data",
                "processing_timestamp": datetime.now().isoformat()
            }
        }
    
    def _process_generic_data(self, json_data: Any) -> Dict[str, Any]:
        """Process generic JSON data."""
        return {
            "data": json_data,
            "metadata": {
                "schema_type": "generic",
                "processing_timestamp": datetime.now().isoformat()
            }
        }
    
    def get_processed_files(self) -> Dict[str, Any]:
        """Get information about all processed files."""
        return self.processed_files
    
    def clear_cache(self):
        """Clear the processed files cache."""
        self.processed_files.clear()
