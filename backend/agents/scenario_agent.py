"""
ScenarioAgent - Dynamic scenario generation and Monte Carlo simulation.

This agent generates economically realistic scenarios based on historical data
and market conditions, with Monte Carlo simulation capabilities.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime
import json
from dataclasses import dataclass
import random
from scipy import stats

from .base_agent import BaseAgent, AgentResponse
from config import get_config

@dataclass
class ScenarioRequest:
    """Request structure for scenario generation."""
    scenario_types: List[str] = None
    simulation_count: int = 1000
    time_horizon: int = 24  # months
    confidence_levels: List[float] = None
    include_monte_carlo: bool = True
    
    def __post_init__(self):
        if self.scenario_types is None:
            self.scenario_types = ["Base", "Optimistic", "Pessimistic"]
        if self.confidence_levels is None:
            self.confidence_levels = [0.95, 0.90, 0.75]

class ScenarioAgent(BaseAgent):
    """
    Agent for dynamic scenario generation and Monte Carlo simulation.
    
    Features:
    - AI-powered scenario generation
    - Monte Carlo simulation
    - Risk assessment and probability analysis
    - Sensitivity analysis
    - Scenario optimization
    - Economic reality validation
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__(
            name="ScenarioAgent",
            role="Scenario Generation and Risk Analysis Specialist",
            goal="Generate realistic scenarios and perform Monte Carlo simulations",
            backstory="Expert in financial modeling and risk analysis with advanced simulation capabilities",
            verbose=verbose
        )
        
        # Scenario cache
        self.generated_scenarios = {}
        self.simulation_results = {}
        
        # Parameter ranges from config
        self.parameter_ranges = self.config.get_hypothesis_ranges()
        
        # Risk factor mappings
        self.risk_factors = {
            "economic": ["gdp_growth", "inflation", "unemployment", "interest_rates"],
            "market": ["competition", "market_saturation", "regulatory_changes"],
            "technology": ["5g_adoption", "esim_adoption", "ai_integration"],
            "operational": ["churn_rate", "arpu_decline", "cost_inflation"],
            "geopolitical": ["trade_tensions", "sanctions", "political_stability"]
        }
        
    def process(self, input_data: Any, **kwargs) -> AgentResponse:
        """
        Process scenario generation request.
        
        Args:
            input_data: ScenarioRequest or dict with parameters
            **kwargs: Additional context like historical_data, market_data
            
        Returns:
            AgentResponse with generated scenarios and simulations
        """
        try:
            # Parse input - handle both ScenarioRequest format and frontend format
            if isinstance(input_data, dict):
                # Check if it's frontend format (with base_assumptions, constraints, etc.)
                if "base_assumptions" in input_data or "constraints" in input_data:
                    # Convert frontend format to ScenarioRequest
                    request = ScenarioRequest(
                        simulation_count=input_data.get("simulation_count", 1000),
                        scenario_types=["Base", "Optimistic", "Pessimistic"],
                        time_horizon=24,
                        include_monte_carlo=True
                    )
                    # Store original data for use in calculations
                    kwargs["frontend_data"] = input_data
                else:
                    # Standard ScenarioRequest format
                    request = ScenarioRequest(**input_data)
            elif isinstance(input_data, ScenarioRequest):
                request = input_data
            else:
                return AgentResponse(
                    success=False,
                    error="Input must be ScenarioRequest or dict"
                )
            
            # Generate base scenarios
            scenarios_result = self._generate_scenarios(request, **kwargs)
            if not scenarios_result.success:
                return scenarios_result
            
            # Run Monte Carlo simulation if requested (using pure math service)
            monte_carlo_result = None
            if request.include_monte_carlo:
                monte_carlo_result = self._run_monte_carlo_with_service(request, scenarios_result.data, **kwargs)
            
            # Perform risk analysis
            risk_analysis_result = self._analyze_risks(scenarios_result.data, **kwargs)
            
            # Generate optimization recommendations
            optimization_result = self._optimize_scenarios(scenarios_result.data, **kwargs)
            
            # Store results
            self.generated_scenarios = scenarios_result.data
            if monte_carlo_result and monte_carlo_result.success:
                self.simulation_results = monte_carlo_result.data
            
            return AgentResponse(
                success=True,
                data={
                    "scenarios": scenarios_result.data,
                    "monte_carlo": monte_carlo_result.data if monte_carlo_result and monte_carlo_result.success else None,
                    "risk_analysis": risk_analysis_result.data if risk_analysis_result.success else None,
                    "optimization": optimization_result.data if optimization_result.success else None,
                    "summary": self._generate_summary(scenarios_result.data, request),
                    "metadata": {
                        "request": request.__dict__,
                        "generation_date": datetime.now().isoformat(),
                        "simulation_count": request.simulation_count if request.include_monte_carlo else 0
                    }
                },
                message="Scenario generation completed successfully"
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Scenario generation failed: {str(e)}"
            )
    
    def _generate_scenarios(self, request: ScenarioRequest, **kwargs) -> AgentResponse:
        """Generate AI-powered scenarios."""
        historical_data = kwargs.get("historical_data", {})
        market_data = kwargs.get("market_data", {})
        
        prompt = f"""
        Generate comprehensive business scenarios for a multi-country telco and fintech company.
        
        Context:
        - Business units: Virgin Mobile (Chile, Colombia, Mexico, Kuwait, KSA), FriendyMobile (Oman, KSA), FriendiPay (KSA, Oman), Virgin Connect Roam
        - Markets: Latin America (Chile, Colombia, Mexico) and GCC (Oman, Kuwait, KSA)
        - Time horizon: {request.time_horizon} months
        - Scenario types: {request.scenario_types}
        
        Historical context: {json.dumps(historical_data, indent=2, default=str)[:1000]}
        
        Market data: {json.dumps(market_data, indent=2, default=str)[:1000]}
        
        Generate scenarios considering:
        1. Macro-economic conditions by region
        2. Technology adoption (5G, eSIM, AI)
        3. Regulatory environment
        4. Competition and market dynamics
        5. Geopolitical factors
        6. Consumer behavior changes
        
        Respond with valid JSON format:
        {{
            "Base": {{
                "name": "Base Case Scenario",
                "description": "Most likely scenario based on current trends",
                "probability": 0.5,
                "parameters": {{
                    "gdp_growth_chile": 2.5,
                    "gdp_growth_colombia": 3.0,
                    "gdp_growth_mexico": 2.8,
                    "gdp_growth_oman": 3.2,
                    "gdp_growth_kuwait": 2.9,
                    "gdp_growth_ksa": 4.1,
                    "inflation_chile": 4.0,
                    "inflation_colombia": 5.5,
                    "inflation_mexico": 4.2,
                    "inflation_oman": 2.8,
                    "inflation_kuwait": 3.1,
                    "inflation_ksa": 2.5,
                    "subscriber_growth_virgin_mobile": 8.5,
                    "subscriber_growth_friendymobile": 12.0,
                    "subscriber_growth_friendipay": 25.0,
                    "arpu_growth_virgin_mobile": 3.2,
                    "arpu_growth_friendymobile": 2.8,
                    "arpu_growth_friendipay": 8.5,
                    "esim_adoption_rate": 15.0,
                    "ai_adoption_rate": 12.0,
                    "fx_volatility_factor": 1.0
                }},
                "key_assumptions": ["list of key assumptions"],
                "risk_factors": ["list of risk factors"],
                "triggers": ["events that would invalidate this scenario"]
            }},
            "Optimistic": {{
                "name": "Optimistic Scenario",
                "description": "Best case scenario with favorable conditions",
                "probability": 0.25,
                "parameters": {{...}},
                "key_assumptions": ["list of optimistic assumptions"],
                "risk_factors": ["list of risk factors"],
                "triggers": ["events that would enable this scenario"]
            }},
            "Pessimistic": {{
                "name": "Pessimistic Scenario", 
                "description": "Worst case scenario with challenging conditions",
                "probability": 0.25,
                "parameters": {{...}},
                "key_assumptions": ["list of pessimistic assumptions"],
                "risk_factors": ["list of risk factors"],
                "triggers": ["events that would lead to this scenario"]
            }}
        }}
        
        Ensure all property names and string values are properly quoted with double quotes.
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            # Validate scenarios
            validated_scenarios = self._validate_scenarios(response.data)
            return AgentResponse(
                success=True,
                data=validated_scenarios,
                message="Scenarios generated successfully"
            )
        else:
            return response
    
    def _validate_scenarios(self, scenarios: Dict[str, Any]) -> Dict[str, Any]:
        """Validate scenario parameters against defined ranges."""
        validated = {}
        
        for scenario_name, scenario_data in scenarios.items():
            validated_scenario = scenario_data.copy()
            validated_parameters = {}
            
            parameters = scenario_data.get("parameters", {})
            
            for param_name, param_value in parameters.items():
                if param_name in self.parameter_ranges:
                    param_range = self.parameter_ranges[param_name]
                    min_val = param_range["min"]
                    max_val = param_range["max"]
                    
                    # Clamp values to valid range
                    validated_value = max(min_val, min(max_val, param_value))
                    validated_parameters[param_name] = validated_value
                    
                    # Log if value was adjusted
                    if validated_value != param_value:
                        if self.verbose:
                            print(f"Adjusted {param_name} from {param_value} to {validated_value}")
                else:
                    validated_parameters[param_name] = param_value
            
            validated_scenario["parameters"] = validated_parameters
            validated[scenario_name] = validated_scenario
        
        return validated
    
    def _run_monte_carlo(self, request: ScenarioRequest, scenarios: Dict[str, Any], **kwargs) -> AgentResponse:
        """Run Monte Carlo simulation."""
        try:
            simulation_results = {}
            
            # Extract parameter distributions from scenarios
            base_params = scenarios.get("Base", {}).get("parameters", {})
            optimistic_params = scenarios.get("Optimistic", {}).get("parameters", {})
            pessimistic_params = scenarios.get("Pessimistic", {}).get("parameters", {})
            
            # Generate parameter distributions
            param_distributions = self._create_parameter_distributions(
                base_params, optimistic_params, pessimistic_params
            )
            
            # Run simulations
            simulation_data = []
            for i in range(request.simulation_count):
                # Sample parameters from distributions
                sampled_params = self._sample_parameters(param_distributions)

                # Calculate outcome metrics with uploaded data
                outcome = self._calculate_scenario_outcome(sampled_params, **kwargs)

                simulation_data.append({
                    "simulation_id": i,
                    "parameters": sampled_params,
                    "outcome": outcome
                })
            
            # Analyze simulation results
            analysis_result = self._analyze_simulation_results(simulation_data, request.confidence_levels)
            
            simulation_results = {
                "simulation_data": simulation_data,
                "analysis": analysis_result,
                "parameter_distributions": param_distributions,
                "confidence_intervals": self._calculate_confidence_intervals(simulation_data, request.confidence_levels)
            }
            
            return AgentResponse(
                success=True,
                data=simulation_results,
                message=f"Monte Carlo simulation completed with {request.simulation_count} iterations"
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Monte Carlo simulation failed: {str(e)}"
            )
    
    def _create_parameter_distributions(self, base_params: Dict[str, Any], optimistic_params: Dict[str, Any], pessimistic_params: Dict[str, Any]) -> Dict[str, Any]:
        """Create parameter distributions for Monte Carlo simulation."""
        distributions = {}
        
        for param_name in base_params.keys():
            base_val = base_params.get(param_name, 0)
            opt_val = optimistic_params.get(param_name, base_val)
            pess_val = pessimistic_params.get(param_name, base_val)
            
            # Create triangular distribution
            distributions[param_name] = {
                "type": "triangular",
                "mode": base_val,
                "left": min(pess_val, base_val, opt_val),
                "right": max(pess_val, base_val, opt_val)
            }
        
        return distributions
    
    def _sample_parameters(self, distributions: Dict[str, Any]) -> Dict[str, Any]:
        """Sample parameters from their distributions."""
        sampled = {}
        
        for param_name, dist_info in distributions.items():
            if dist_info["type"] == "triangular":
                # Use numpy triangular distribution
                sampled[param_name] = np.random.triangular(
                    dist_info["left"],
                    dist_info["mode"],
                    dist_info["right"]
                )
            else:
                # Default to normal distribution
                sampled[param_name] = np.random.normal(
                    dist_info.get("mean", 0),
                    dist_info.get("std", 1)
                )
        
        return sampled
    
    def _calculate_scenario_outcome(self, parameters: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Calculate outcome metrics for a given set of parameters."""
        # Simplified outcome calculation (in production, this would be more complex)
        
        # Calculate revenue impact
        revenue_factors = [
            parameters.get("subscriber_growth_virgin_mobile", 0) * 0.3,
            parameters.get("subscriber_growth_friendymobile", 0) * 0.2,
            parameters.get("subscriber_growth_friendipay", 0) * 0.1,
            parameters.get("arpu_growth_virgin_mobile", 0) * 0.4,
            parameters.get("arpu_growth_friendymobile", 0) * 0.3,
            parameters.get("arpu_growth_friendipay", 0) * 0.2
        ]
        
        revenue_impact = sum(revenue_factors) / 100  # Convert to percentage
        
        # Calculate cost impact
        cost_factors = [
            parameters.get("inflation_chile", 0) * 0.15,
            parameters.get("inflation_colombia", 0) * 0.15,
            parameters.get("inflation_mexico", 0) * 0.15,
            parameters.get("inflation_oman", 0) * 0.1,
            parameters.get("inflation_kuwait", 0) * 0.1,
            parameters.get("inflation_ksa", 0) * 0.25
        ]
        
        cost_impact = sum(cost_factors) / 100
        
        # Calculate FX impact
        fx_impact = parameters.get("fx_volatility_factor", 1.0) - 1.0
        
        # Calculate final metrics
        outcome = {
            "revenue_growth": revenue_impact,
            "cost_inflation": cost_impact,
            "fx_impact": fx_impact,
            "net_profit_margin": revenue_impact - cost_impact - abs(fx_impact) * 0.1,
            "risk_score": abs(fx_impact) * 0.5 + cost_impact * 0.3 + max(0, -revenue_impact) * 0.2
        }
        
        # Enhance outcome with uploaded data if available
        uploaded_data = kwargs.get("uploaded_data", {})
        frontend_data = kwargs.get("frontend_data", {})

        if uploaded_data:
            outcome = self._enhance_outcome_with_uploaded_data(outcome, uploaded_data, parameters, frontend_data)

        return outcome

    def _enhance_outcome_with_uploaded_data(self, outcome: Dict[str, Any], uploaded_data: Dict[str, Any], parameters: Dict[str, Any], frontend_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Enhance outcome calculations using actual uploaded OPEX and CAPEX data."""
        try:
            opex_data = uploaded_data.get("opex_data", [])
            capex_data = uploaded_data.get("capex_data", [])

            # Calculate base values from uploaded data
            base_opex = self._calculate_base_opex(opex_data)
            base_capex = self._calculate_base_capex(capex_data)

            # Get parameters from frontend data if available
            if frontend_data:
                base_assumptions = frontend_data.get("base_assumptions", {})
                constraints = frontend_data.get("constraints", {})

                revenue_growth = base_assumptions.get("revenue_growth", 0.05)
                cost_inflation = outcome.get("cost_inflation", 0.03)

                # Use constraints as limits
                opex_limit = constraints.get("opex_limit", base_opex)
                capex_limit = constraints.get("capex_limit", base_capex)
            else:
                revenue_growth = outcome.get("revenue_growth", 0.05)
                cost_inflation = outcome.get("cost_inflation", 0.03)
                opex_limit = base_opex
                capex_limit = base_capex

            # Calculate projected values
            base_revenue = base_opex * 1.25  # Assume 25% margin over OPEX
            projected_revenue = base_revenue * (1 + revenue_growth)
            projected_opex = min(base_opex * (1 + cost_inflation), opex_limit)
            projected_capex = min(base_capex, capex_limit)

            # Update outcome with actual data
            outcome.update({
                "base_opex": base_opex,
                "base_capex": base_capex,
                "base_revenue": base_revenue,
                "projected_revenue": projected_revenue,
                "projected_opex": projected_opex,
                "projected_capex": projected_capex,
                "total_costs": projected_opex + projected_capex,
                "net_income": projected_revenue - (projected_opex + projected_capex),
                "margin": (projected_revenue - (projected_opex + projected_capex)) / projected_revenue if projected_revenue > 0 else 0,
                "uses_uploaded_data": True,
                "data_sources": {
                    "opex_files": len(opex_data),
                    "capex_files": len(capex_data)
                }
            })

        except Exception as e:
            self.logger.error(f"Error enhancing outcome with uploaded data: {str(e)}")
            outcome["data_enhancement_error"] = str(e)

        return outcome

    def _calculate_base_opex(self, opex_data: List[Dict[str, Any]]) -> float:
        """Calculate base OPEX from uploaded data."""
        if not opex_data:
            return 50_000_000  # Default $50M if no data

        total_opex = 0
        for opex_file in opex_data:
            try:
                data = opex_file.get("data", [])
                if isinstance(data, list) and data:
                    # Look for cost/amount columns
                    for record in data:
                        if isinstance(record, dict):
                            # Try different column names for cost amounts
                            for col in ["amount", "cost", "value", "total", "expense"]:
                                if col in record and isinstance(record[col], (int, float)):
                                    total_opex += record[col]
                                    break
            except Exception as e:
                self.logger.warning(f"Error processing OPEX file {opex_file.get('filename', 'unknown')}: {str(e)}")
                continue

        return total_opex if total_opex > 0 else 50_000_000

    def _calculate_base_capex(self, capex_data: List[Dict[str, Any]]) -> float:
        """Calculate base CAPEX from uploaded data."""
        if not capex_data:
            return 25_000_000  # Default $25M if no data

        total_capex = 0
        for capex_file in capex_data:
            try:
                data = capex_file.get("data", {})
                if isinstance(data, dict):
                    # Handle JSON structure with projects
                    if "projects" in data:
                        projects = data["projects"]
                        for project in projects:
                            if isinstance(project, dict) and "total_amount" in project:
                                total_capex += project["total_amount"]
                    elif "total_amount" in data:
                        total_capex += data["total_amount"]
                elif isinstance(data, list):
                    # Handle tabular data
                    for record in data:
                        if isinstance(record, dict):
                            for col in ["total_amount", "amount", "investment", "capex", "value"]:
                                if col in record and isinstance(record[col], (int, float)):
                                    total_capex += record[col]
                                    break
            except Exception as e:
                self.logger.warning(f"Error processing CAPEX file {capex_file.get('filename', 'unknown')}: {str(e)}")
                continue

        return total_capex if total_capex > 0 else 25_000_000

    def _run_monte_carlo_with_service(self, request: ScenarioRequest, scenarios: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Run Monte Carlo simulation using the dedicated math service.
        Fast, reliable, and cost-effective - no AI overhead.
        """
        try:
            from services.monte_carlo_service import MonteCarloService

            # Get uploaded data and frontend parameters
            uploaded_data = kwargs.get("uploaded_data", {})
            frontend_data = kwargs.get("frontend_data", {})

            if not uploaded_data:
                self.logger.warning("No uploaded data available for Monte Carlo simulation")
                return {"error": "No uploaded data available"}

            # Initialize Monte Carlo service
            mc_service = MonteCarloService()

            # Run simulation
            self.logger.info(f"Running Monte Carlo simulation with {request.simulation_count} iterations")
            start_time = datetime.now()

            results = mc_service.run_simulation(
                uploaded_data=uploaded_data,
                frontend_params=frontend_data,
                n_simulations=request.simulation_count
            )

            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            self.logger.info(f"Monte Carlo simulation completed in {execution_time:.2f} seconds")

            # Convert results to dictionary format
            return {
                "simulation_data": results.simulation_data[:100],  # Limit data for response size
                "summary_statistics": results.summary_stats,
                "confidence_intervals": results.confidence_intervals,
                "risk_metrics": results.risk_metrics,
                "metadata": {
                    **results.metadata,
                    "execution_time_seconds": execution_time,
                    "service_type": "pure_math",
                    "ai_calls_used": 0  # No AI calls for math
                },
                "insights": self._generate_monte_carlo_insights(results)
            }

        except Exception as e:
            self.logger.error(f"Monte Carlo service error: {str(e)}")
            return {"error": f"Monte Carlo simulation failed: {str(e)}"}

    def _generate_monte_carlo_insights(self, results) -> List[str]:
        """Generate business insights from Monte Carlo results using AI."""
        try:
            # Use AI only for generating insights, not for calculations
            insights_prompt = f"""
            Based on these Monte Carlo simulation results, provide 3-5 key business insights:

            Summary Statistics:
            - Average Net Income: ${results.summary_stats.get('net_income_mean', 0):,.0f}
            - Net Income Range: ${results.summary_stats.get('net_income_min', 0):,.0f} to ${results.summary_stats.get('net_income_max', 0):,.0f}
            - Average Margin: {results.summary_stats.get('margin_mean', 0):.1%}

            Risk Metrics:
            - Probability of Loss: {results.risk_metrics.get('probability_of_loss', 0):.1%}
            - Value at Risk (95%): ${results.risk_metrics.get('value_at_risk_95', 0):,.0f}
            - Probability of Positive Margin: {results.risk_metrics.get('probability_positive_margin', 0):.1%}

            Data Sources:
            - OPEX Files: {results.metadata.get('opex_files_used', 0)}
            - CAPEX Files: {results.metadata.get('capex_files_used', 0)}
            - Base OPEX: ${results.metadata.get('base_opex', 0):,.0f}
            - Base CAPEX: ${results.metadata.get('base_capex', 0):,.0f}

            Provide actionable business insights in bullet points.
            """

            response = self._llm_call(insights_prompt)
            if response.success and response.data:
                # Parse insights from AI response
                insights_text = response.data.get("content", "")
                insights = [line.strip() for line in insights_text.split('\n') if line.strip() and ('•' in line or '-' in line)]
                return insights[:5]  # Limit to 5 insights

            return ["Monte Carlo simulation completed successfully with uploaded data"]

        except Exception as e:
            self.logger.error(f"Error generating insights: {str(e)}")
            return ["Monte Carlo simulation completed - insights generation failed"]

    def _analyze_simulation_results(self, simulation_data: List[Dict[str, Any]], confidence_levels: List[float]) -> Dict[str, Any]:
        """Analyze Monte Carlo simulation results."""
        # Extract outcomes
        outcomes = [sim["outcome"] for sim in simulation_data]
        
        # Calculate statistics for each metric
        metrics = ["revenue_growth", "cost_inflation", "fx_impact", "net_profit_margin", "risk_score"]
        analysis = {}
        
        for metric in metrics:
            values = [outcome[metric] for outcome in outcomes]
            
            analysis[metric] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "median": np.median(values),
                "min": np.min(values),
                "max": np.max(values),
                "percentiles": {
                    "p10": np.percentile(values, 10),
                    "p25": np.percentile(values, 25),
                    "p75": np.percentile(values, 75),
                    "p90": np.percentile(values, 90)
                }
            }
        
        return analysis
    
    def _calculate_confidence_intervals(self, simulation_data: List[Dict[str, Any]], confidence_levels: List[float]) -> Dict[str, Any]:
        """Calculate confidence intervals for simulation results."""
        outcomes = [sim["outcome"] for sim in simulation_data]
        confidence_intervals = {}
        
        for confidence_level in confidence_levels:
            alpha = 1 - confidence_level
            lower_percentile = (alpha / 2) * 100
            upper_percentile = (1 - alpha / 2) * 100
            
            intervals = {}
            for metric in ["revenue_growth", "cost_inflation", "fx_impact", "net_profit_margin", "risk_score"]:
                values = [outcome[metric] for outcome in outcomes]
                intervals[metric] = {
                    "lower": np.percentile(values, lower_percentile),
                    "upper": np.percentile(values, upper_percentile)
                }
            
            confidence_intervals[f"{int(confidence_level*100)}%"] = intervals
        
        return confidence_intervals
    
    def _analyze_risks(self, scenarios: Dict[str, Any], **kwargs) -> AgentResponse:
        """Analyze risks across scenarios."""
        prompt = f"""
        Analyze risks across the generated scenarios for a multi-country telco and fintech company.
        
        Scenarios: {json.dumps(scenarios, indent=2, default=str)}
        
        Analyze risks in the following categories:
        1. Economic risks (GDP, inflation, FX)
        2. Market risks (competition, saturation, regulation)
        3. Technology risks (adoption, disruption, security)
        4. Operational risks (churn, costs, capacity)
        5. Geopolitical risks (trade, sanctions, stability)
        
        Provide comprehensive risk analysis in JSON format:
        {{
            "risk_summary": {{
                "overall_risk_level": "high/medium/low",
                "key_risk_factors": ["top 5 risk factors"],
                "risk_correlation": "how risks are interconnected"
            }},
            "risk_categories": {{
                "economic": {{
                    "risk_level": "high/medium/low",
                    "key_risks": ["specific economic risks"],
                    "mitigation_strategies": ["risk mitigation approaches"],
                    "probability": 0.3
                }},
                "market": {{...}},
                "technology": {{...}},
                "operational": {{...}},
                "geopolitical": {{...}}
            }},
            "scenario_risk_comparison": {{
                "Base": {{"risk_score": 0.5, "key_risks": ["..."]}},
                "Optimistic": {{"risk_score": 0.3, "key_risks": ["..."]}},
                "Pessimistic": {{"risk_score": 0.8, "key_risks": ["..."]}}
            }},
            "risk_monitoring": {{
                "early_warning_indicators": ["indicators to monitor"],
                "trigger_events": ["events that would change risk profile"],
                "monitoring_frequency": "recommended monitoring frequency"
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Risk analysis completed successfully"
            )
        else:
            return response
    
    def _optimize_scenarios(self, scenarios: Dict[str, Any], **kwargs) -> AgentResponse:
        """Generate optimization recommendations for scenarios."""
        prompt = f"""
        Provide optimization recommendations for the generated scenarios.
        
        Scenarios: {json.dumps(scenarios, indent=2, default=str)}
        
        Analyze and recommend optimizations in JSON format:
        {{
            "scenario_optimization": {{
                "recommended_scenario": "which scenario to plan for",
                "probability_adjustments": "suggested probability adjustments",
                "parameter_refinements": "suggested parameter refinements"
            }},
            "strategic_recommendations": {{
                "investment_priorities": ["key investment areas"],
                "market_focus": ["markets to prioritize"],
                "product_development": ["product development priorities"],
                "risk_mitigation": ["risk mitigation strategies"]
            }},
            "tactical_recommendations": {{
                "pricing_strategy": "pricing recommendations",
                "cost_management": "cost management priorities",
                "resource_allocation": "resource allocation suggestions",
                "timeline_adjustments": "timeline recommendations"
            }},
            "contingency_planning": {{
                "trigger_points": ["when to adjust strategy"],
                "alternative_scenarios": ["alternative scenarios to consider"],
                "flexibility_measures": ["measures to maintain flexibility"]
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Optimization recommendations generated successfully"
            )
        else:
            return response
    
    def _generate_summary(self, scenarios: Dict[str, Any], request: ScenarioRequest) -> Dict[str, Any]:
        """Generate summary of scenario generation results."""
        summary = {
            "scenarios_generated": len(scenarios),
            "scenario_types": list(scenarios.keys()),
            "time_horizon_months": request.time_horizon,
            "monte_carlo_enabled": request.include_monte_carlo,
            "simulation_count": request.simulation_count if request.include_monte_carlo else 0,
            "confidence_levels": request.confidence_levels,
            "parameter_count": len(scenarios.get("Base", {}).get("parameters", {})),
            "total_probabilities": sum(scenario.get("probability", 0) for scenario in scenarios.values())
        }
        
        return summary
    
    def get_scenario_comparison(self, metric: str) -> AgentResponse:
        """Compare scenarios for a specific metric."""
        if not self.generated_scenarios:
            return AgentResponse(
                success=False,
                error="No scenarios generated yet"
            )
        
        comparison = {}
        for scenario_name, scenario_data in self.generated_scenarios.items():
            parameters = scenario_data.get("parameters", {})
            if metric in parameters:
                comparison[scenario_name] = {
                    "value": parameters[metric],
                    "probability": scenario_data.get("probability", 0)
                }
        
        return AgentResponse(
            success=True,
            data=comparison,
            message=f"Scenario comparison for {metric} completed"
        )
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get the capabilities of the ScenarioAgent."""
        return {
            "name": self.name,
            "description": "Dynamic scenario generation and Monte Carlo simulation",
            "capabilities": [
                "AI-powered scenario generation",
                "Monte Carlo simulation",
                "Risk assessment and analysis",
                "Sensitivity analysis",
                "Scenario optimization",
                "Probability modeling",
                "Confidence interval calculation",
                "Economic reality validation"
            ],
            "supported_parameters": list(self.parameter_ranges.keys()),
            "risk_categories": list(self.risk_factors.keys()),
            "simulation_types": ["Monte Carlo", "Sensitivity Analysis", "Stress Testing"],
            "output_formats": ["JSON", "Statistical Summary", "Confidence Intervals"]
        }