"""
CapexAgent - AI-powered CAPEX planning and technology roadmap integration.

This agent handles capital expenditure planning with intelligent recommendations
for technology investments, cloud migration, and infrastructure upgrades.
"""

import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import json
from dataclasses import dataclass

from .base_agent import BaseAgent, AgentResponse
from config import get_config

@dataclass
class CapexRequest:
    """Request structure for CAPEX planning."""
    business_units: List[str]
    planning_horizon: int = 24  # months
    categories: List[str] = None
    budget_constraints: Dict[str, float] = None
    wizard_projects: List[Dict] = None  # Wizard format projects
    total_amount: float = 0  # Total budget from wizard

    def __post_init__(self):
        if self.categories is None:
            self.categories = [
                "Technology Infrastructure",
                "Cloud Migration",
                "Network Expansion",
                "AI/ML Integration",
                "Security Upgrades",
                "Regulatory Compliance"
            ]
        if self.budget_constraints is None:
            self.budget_constraints = {}
        if self.wizard_projects is None:
            self.wizard_projects = []

class CapexAgent(BaseAgent):
    """
    Agent for CAPEX planning and technology roadmap integration.
    
    Features:
    - AI-powered technology roadmap analysis
    - Cloud migration cost modeling
    - Infrastructure upgrade recommendations
    - Regulatory compliance planning
    - Investment prioritization
    - ROI analysis and forecasting
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__(
            name="CapexAgent",
            role="CAPEX Planning and Technology Investment Specialist",
            goal="Generate intelligent CAPEX plans with technology roadmap integration",
            backstory="Expert in technology investments with advanced forecasting capabilities",
            verbose=verbose
        )
        
        # Technology categories and typical costs
        self.technology_categories = {
            "5G Network Infrastructure": {
                "cost_per_site": 150000,
                "scaling_factor": 1.0,
                "depreciation_years": 7,
                "maintenance_pct": 0.08
            },
            "Cloud Infrastructure": {
                "migration_cost_pct": 0.15,
                "annual_savings_pct": 0.20,
                "scaling_factor": 0.8,
                "depreciation_years": 3
            },
            "AI/ML Platform": {
                "initial_setup": 500000,
                "per_use_case": 100000,
                "annual_maintenance": 0.25,
                "roi_multiplier": 2.5
            },
            "Security Infrastructure": {
                "base_cost": 200000,
                "per_user_cost": 150,
                "annual_maintenance": 0.20,
                "compliance_premium": 0.30
            },
            "Data Center Expansion": {
                "cost_per_rack": 25000,
                "cooling_multiplier": 1.3,
                "power_infrastructure": 0.40,
                "depreciation_years": 10
            }
        }
        
        # Business unit technology profiles
        self.bu_tech_profiles = {
            "Virgin Mobile Chile": {
                "5g_priority": "high",
                "cloud_readiness": "medium",
                "subscriber_base": 2000000,
                "revenue_per_subscriber": 15
            },
            "Virgin Mobile Colombia": {
                "5g_priority": "high", 
                "cloud_readiness": "medium",
                "subscriber_base": 3500000,
                "revenue_per_subscriber": 12
            },
            "Virgin Mobile Mexico": {
                "5g_priority": "high",
                "cloud_readiness": "high",
                "subscriber_base": 8000000,
                "revenue_per_subscriber": 18
            },
            "FriendyMobile Oman": {
                "5g_priority": "medium",
                "cloud_readiness": "high",
                "subscriber_base": 800000,
                "revenue_per_subscriber": 25
            },
            "FriendiPay KSA": {
                "fintech_focus": "high",
                "cloud_readiness": "high",
                "transaction_volume": 10000000,
                "revenue_per_transaction": 0.05
            }
        }
    
    def process(self, input_data: Any, **kwargs) -> AgentResponse:
        """
        Process CAPEX planning request.

        Args:
            input_data: CapexRequest, dict with planning parameters, or wizard capex_data
            **kwargs: Additional context like market_data, budget_constraints

        Returns:
            AgentResponse with CAPEX recommendations and roadmap
        """
        try:
            # Parse input - handle wizard format
            if isinstance(input_data, dict):
                # Check if this is wizard format with projects
                if 'projects' in input_data and isinstance(input_data['projects'], list):
                    # Convert wizard format to CapexRequest
                    business_units = list(set(p.get('business_unit', 'Unknown') for p in input_data['projects']))
                    request = CapexRequest(
                        business_units=business_units,
                        planning_horizon=24,  # Default
                        categories=list(set(p.get('category', 'Other') for p in input_data['projects'])),
                        budget_constraints={}
                    )
                    # Store wizard projects for later use
                    request.wizard_projects = input_data['projects']
                    request.total_amount = input_data.get('total_amount', 0)
                else:
                    # Legacy format
                    request = CapexRequest(**input_data)
            elif isinstance(input_data, CapexRequest):
                request = input_data
            else:
                return AgentResponse(
                    success=False,
                    error="Input must be CapexRequest, dict, or wizard capex_data"
                )
            
            # Analyze technology needs
            tech_analysis = self._analyze_technology_needs(request, **kwargs)
            if not tech_analysis.success:
                return tech_analysis
            
            # Generate CAPEX recommendations
            capex_recommendations = self._generate_capex_recommendations(
                request, tech_analysis.data, **kwargs
            )
            
            # Create investment roadmap
            roadmap_result = self._create_investment_roadmap(
                request, capex_recommendations.data, **kwargs
            )
            
            # Calculate ROI and financial impact
            roi_analysis = self._calculate_roi_analysis(
                capex_recommendations.data, **kwargs
            )
            
            # Generate implementation plan
            implementation_plan = self._generate_implementation_plan(
                roadmap_result.data, **kwargs
            )
            
            return AgentResponse(
                success=True,
                data={
                    "technology_analysis": tech_analysis.data,
                    "capex_recommendations": capex_recommendations.data,
                    "investment_roadmap": roadmap_result.data,
                    "roi_analysis": roi_analysis.data if roi_analysis.success else None,
                    "implementation_plan": implementation_plan.data if implementation_plan.success else None,
                    "summary": self._generate_summary(request, capex_recommendations.data),
                    "metadata": {
                        "request": request.__dict__,
                        "planning_date": datetime.now().isoformat(),
                        "horizon_months": request.planning_horizon
                    }
                },
                message="CAPEX planning completed successfully"
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"CAPEX planning failed: {str(e)}"
            )
    
    def _analyze_technology_needs(self, request: CapexRequest, **kwargs) -> AgentResponse:
        """Analyze technology needs for each business unit."""
        market_data = kwargs.get("market_data", {})
        
        prompt = f"""
        Analyze technology investment needs for multi-country telco and fintech operations.
        
        Business Units: {request.business_units}
        Planning Horizon: {request.planning_horizon} months
        Technology Categories: {request.categories}
        
        Business Unit Profiles: {json.dumps(self.bu_tech_profiles, indent=2)}
        
        Market Context: {json.dumps(market_data, indent=2, default=str)[:1000]}
        
        Consider:
        1. 5G network rollout requirements
        2. Cloud migration opportunities
        3. AI/ML integration potential
        4. Security and compliance needs
        5. Regulatory requirements by market
        6. Competition and market positioning
        
        Provide analysis in JSON format:
        {{
            "technology_assessment": {{
                "business_unit_name": {{
                    "5g_rollout": {{
                        "priority": "high/medium/low",
                        "investment_required": 5000000,
                        "timeline_months": 18,
                        "expected_roi": 2.5,
                        "risk_factors": ["regulatory delays", "spectrum availability"]
                    }},
                    "cloud_migration": {{
                        "readiness_score": 0.8,
                        "migration_cost": 2000000,
                        "annual_savings": 800000,
                        "timeline_months": 12
                    }},
                    "ai_integration": {{
                        "use_cases": ["customer service", "fraud detection"],
                        "investment_required": 1500000,
                        "productivity_gain": 0.25,
                        "timeline_months": 9
                    }},
                    "security_upgrades": {{
                        "compliance_requirements": ["PCI-DSS", "GDPR"],
                        "investment_required": 800000,
                        "risk_mitigation": "high",
                        "timeline_months": 6
                    }}
                }}
            }},
            "market_trends": {{
                "5g_adoption_rate": 0.35,
                "cloud_first_mandate": true,
                "ai_investment_trend": "accelerating",
                "security_threat_level": "elevated"
            }},
            "investment_priorities": {{
                "tier_1": ["5G infrastructure", "Security upgrades"],
                "tier_2": ["Cloud migration", "AI integration"],
                "tier_3": ["Advanced analytics", "IoT platforms"]
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Technology needs analysis completed"
            )
        else:
            return response
    
    def _generate_capex_recommendations(self, request: CapexRequest, tech_analysis: Dict[str, Any], **kwargs) -> AgentResponse:
        """Generate CAPEX recommendations based on technology analysis and wizard projects."""
        budget_constraints = kwargs.get("budget_constraints", request.budget_constraints)

        # Check if we have wizard projects to incorporate
        wizard_projects = getattr(request, 'wizard_projects', [])

        if wizard_projects:
            # Process wizard projects into recommendations format
            capex_recommendations = self._process_wizard_projects(wizard_projects, tech_analysis)
        else:
            # Generate AI-based recommendations using existing logic
            capex_recommendations = self._generate_ai_recommendations(request, tech_analysis, budget_constraints)

        return AgentResponse(
            success=True,
            data=capex_recommendations,
            message="CAPEX recommendations generated successfully"
        )

    def _process_wizard_projects(self, wizard_projects: List[Dict], tech_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Process wizard projects into CAPEX recommendations format."""
        # Group projects by business unit
        bu_recommendations = {}
        total_capex = 0
        capex_by_category = {}
        capex_by_year = {}

        for project in wizard_projects:
            bu = project.get('business_unit', 'Unknown')
            if bu not in bu_recommendations:
                bu_recommendations[bu] = {
                    "total_capex": 0,
                    "investments": []
                }

            # Convert wizard project to investment format
            investment = {
                "category": project.get('category', 'Other'),
                "description": project.get('description', project.get('name', 'Unnamed project')),
                "cost": project.get('total_amount', 0),
                "timeline": {
                    "start_month": 1,  # Default to month 1
                    "duration_months": project.get('duration_months', 12),
                    "milestones": [f"Phase 1: Planning", f"Phase 2: Implementation"]
                },
                "business_case": {
                    "revenue_impact": project.get('total_amount', 0) * project.get('roi_expected', 0.15),
                    "cost_savings": project.get('total_amount', 0) * 0.05,  # Assume 5% cost savings
                    "payback_period": 1 / max(project.get('roi_expected', 0.15), 0.01),
                    "npv": project.get('total_amount', 0) * project.get('roi_expected', 0.15) * 3  # Simple NPV estimate
                },
                "risk_factors": self._get_risk_factors_for_category(project.get('category', 'Other')),
                "dependencies": self._get_dependencies_for_category(project.get('category', 'Other')),
                "priority": project.get('priority', 'Medium'),
                "depreciation_years": project.get('depreciation_years', 5),
                "maintenance_pct": project.get('maintenance_pct', 0.1)
            }

            bu_recommendations[bu]["investments"].append(investment)
            bu_recommendations[bu]["total_capex"] += project.get('total_amount', 0)

            # Update totals
            total_capex += project.get('total_amount', 0)
            category = project.get('category', 'Other')
            capex_by_category[category] = capex_by_category.get(category, 0) + project.get('total_amount', 0)

            # Simple year distribution (could be enhanced based on start_date and duration)
            year_2024 = project.get('total_amount', 0) * 0.4  # 40% in first year
            year_2025 = project.get('total_amount', 0) * 0.6  # 60% in second year
            capex_by_year["2024"] = capex_by_year.get("2024", 0) + year_2024
            capex_by_year["2025"] = capex_by_year.get("2025", 0) + year_2025

        return {
            "capex_recommendations": bu_recommendations,
            "consolidated_summary": {
                "total_capex_all_bu": total_capex,
                "capex_by_category": capex_by_category,
                "capex_by_year": capex_by_year,
                "expected_roi": {
                    "average_roi": sum(p.get('roi_expected', 0.15) for p in wizard_projects) / max(len(wizard_projects), 1),
                    "total_expected_return": sum(p.get('total_amount', 0) * p.get('roi_expected', 0.15) for p in wizard_projects),
                    "payback_period_avg": sum(1 / max(p.get('roi_expected', 0.15), 0.01) for p in wizard_projects) / max(len(wizard_projects), 1)
                },
                "risk_assessment": {
                    "high_risk_projects": len([p for p in wizard_projects if p.get('priority') == 'High']),
                    "total_projects": len(wizard_projects),
                    "risk_score": "medium"  # Could be calculated based on project types
                }
            },
            "wizard_projects_processed": len(wizard_projects)
        }

    def _get_risk_factors_for_category(self, category: str) -> List[str]:
        """Get risk factors for a given category."""
        risk_mapping = {
            "Technology Infrastructure": ["technology obsolescence", "integration complexity", "vendor dependency"],
            "Cloud Migration": ["data migration risks", "downtime", "security concerns"],
            "Network Expansion": ["regulatory approval", "spectrum costs", "site acquisition"],
            "AI/ML Integration": ["data quality", "skill requirements", "model accuracy"],
            "Security Upgrades": ["compliance requirements", "system compatibility", "user adoption"],
            "Regulatory Compliance": ["regulatory changes", "implementation timeline", "audit requirements"],
            "Data Center": ["power requirements", "cooling costs", "location risks"],
            "Other": ["project complexity", "resource availability", "market conditions"]
        }
        return risk_mapping.get(category, ["general project risks"])

    def _get_dependencies_for_category(self, category: str) -> List[str]:
        """Get dependencies for a given category."""
        dependency_mapping = {
            "Technology Infrastructure": ["network planning", "vendor selection", "staff training"],
            "Cloud Migration": ["data assessment", "security framework", "migration planning"],
            "Network Expansion": ["site surveys", "regulatory approvals", "equipment procurement"],
            "AI/ML Integration": ["data preparation", "model development", "infrastructure setup"],
            "Security Upgrades": ["security assessment", "policy updates", "staff training"],
            "Regulatory Compliance": ["regulatory analysis", "process design", "system updates"],
            "Data Center": ["site preparation", "power infrastructure", "cooling systems"],
            "Other": ["project planning", "resource allocation", "stakeholder approval"]
        }
        return dependency_mapping.get(category, ["project planning"])

    def _generate_ai_recommendations(self, request: CapexRequest, tech_analysis: Dict[str, Any], budget_constraints: Dict) -> Dict[str, Any]:
        """Generate AI-based recommendations using existing logic."""
        prompt = f"""
        Generate specific CAPEX recommendations based on technology analysis.

        Technology Analysis: {json.dumps(tech_analysis, indent=2, default=str)}

        Budget Constraints: {json.dumps(budget_constraints, indent=2)}

        Technology Cost Models: {json.dumps(self.technology_categories, indent=2)}

        Generate detailed CAPEX recommendations in JSON format:
        {{
            "capex_recommendations": {{
                "business_unit_name": {{
                    "total_capex": 15000000,
                    "investments": [
                        {{
                            "category": "5G Infrastructure",
                            "description": "5G network rollout in major cities",
                            "cost": 8000000,
                            "timeline": {{
                                "start_month": 1,
                                "duration_months": 18,
                                "milestones": ["Phase 1: Planning", "Phase 2: Deployment"]
                            }},
                            "business_case": {{
                                "revenue_impact": 3000000,
                                "cost_savings": 500000,
                                "payback_period": 2.5,
                                "npv": 5000000
                            }},
                            "risk_factors": ["regulatory approval", "spectrum costs"],
                            "dependencies": ["network planning", "vendor selection"]
                        }}
                    ]
                }}
            }},
            "consolidated_summary": {{
                "total_capex_all_bu": 45000000,
                "capex_by_category": {{
                    "5G Infrastructure": 25000000,
                    "Cloud Migration": 8000000,
                    "AI Integration": 6000000,
                    "Security": 3000000,
                    "Other": 3000000
                }},
                "capex_by_year": {{
                    "2024": 20000000,
                    "2025": 25000000
                }},
                "expected_roi": {{
                    "total_npv": 15000000,
                    "average_payback": 2.8,
                    "irr": 0.22
                }}
            }}
        }}
        """

        response = self._call_llm(prompt, json_mode=True)
        if not response.success:
            raise ValueError("Failed to generate AI recommendations")
        return response.data
    
    def _create_investment_roadmap(self, request: CapexRequest, capex_data: Dict[str, Any], **kwargs) -> AgentResponse:
        """Create detailed investment roadmap with sequencing and dependencies."""
        prompt = f"""
        Create a comprehensive investment roadmap based on CAPEX recommendations.
        
        CAPEX Recommendations: {json.dumps(capex_data, indent=2, default=str)}
        
        Planning Horizon: {request.planning_horizon} months
        
        Create detailed roadmap in JSON format:
        {{
            "investment_roadmap": {{
                "phases": [
                    {{
                        "phase_name": "Foundation Phase",
                        "duration_months": 6,
                        "investments": [
                            {{
                                "project": "Security Infrastructure Upgrade",
                                "business_units": ["Virgin Mobile Chile"],
                                "budget": 2000000,
                                "start_month": 1,
                                "end_month": 6,
                                "critical_path": true,
                                "dependencies": [],
                                "deliverables": ["Security compliance", "Risk mitigation"]
                            }}
                        ],
                        "total_budget": 5000000,
                        "success_criteria": ["Compliance achieved", "Infrastructure secured"]
                    }}
                ],
                "resource_requirements": {{
                    "internal_team": 25,
                    "external_consultants": 15,
                    "vendor_resources": 50,
                    "budget_allocation": {{
                        "hardware": 0.4,
                        "software": 0.3,
                        "services": 0.2,
                        "contingency": 0.1
                    }}
                }},
                "risk_mitigation": {{
                    "high_risk_projects": ["5G rollout in Colombia"],
                    "mitigation_strategies": ["Phased deployment", "Vendor diversification"],
                    "contingency_plans": ["Alternative technology", "Budget reallocation"]
                }},
                "governance": {{
                    "decision_gates": ["Phase completion", "Budget approval"],
                    "reporting_frequency": "monthly",
                    "escalation_process": "defined"
                }}
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Investment roadmap created"
            )
        else:
            return response
    
    def _calculate_roi_analysis(self, capex_data: Dict[str, Any], **kwargs) -> AgentResponse:
        """Calculate detailed ROI analysis for investments."""
        prompt = f"""
        Calculate comprehensive ROI analysis for CAPEX investments.
        
        CAPEX Data: {json.dumps(capex_data, indent=2, default=str)}
        
        Calculate detailed financial analysis in JSON format:
        {{
            "roi_analysis": {{
                "investment_summary": {{
                    "total_investment": 45000000,
                    "investment_by_category": {{}},
                    "timeline": "24 months"
                }},
                "financial_projections": {{
                    "revenue_impact": {{
                        "year_1": 5000000,
                        "year_2": 12000000,
                        "year_3": 18000000,
                        "total_3_year": 35000000
                    }},
                    "cost_savings": {{
                        "operational_savings": 8000000,
                        "efficiency_gains": 5000000,
                        "maintenance_reduction": 2000000
                    }},
                    "cash_flow": {{
                        "year_1": -15000000,
                        "year_2": -5000000,
                        "year_3": 8000000,
                        "year_4": 15000000,
                        "year_5": 18000000
                    }}
                }},
                "roi_metrics": {{
                    "net_present_value": 25000000,
                    "internal_rate_of_return": 0.28,
                    "payback_period": 2.3,
                    "return_on_investment": 1.8,
                    "profitability_index": 1.5
                }},
                "sensitivity_analysis": {{
                    "best_case": {{
                        "npv": 35000000,
                        "irr": 0.35,
                        "assumptions": ["faster adoption", "higher revenue"]
                    }},
                    "worst_case": {{
                        "npv": 10000000,
                        "irr": 0.15,
                        "assumptions": ["slower adoption", "increased costs"]
                    }}
                }},
                "risk_assessment": {{
                    "technology_risk": "medium",
                    "market_risk": "low",
                    "execution_risk": "medium",
                    "regulatory_risk": "low",
                    "overall_risk": "medium"
                }}
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="ROI analysis completed"
            )
        else:
            return response
    
    def _generate_implementation_plan(self, roadmap_data: Dict[str, Any], **kwargs) -> AgentResponse:
        """Generate detailed implementation plan with tasks and timelines."""
        prompt = f"""
        Generate detailed implementation plan for investment roadmap.
        
        Roadmap Data: {json.dumps(roadmap_data, indent=2, default=str)}
        
        Create comprehensive implementation plan in JSON format:
        {{
            "implementation_plan": {{
                "program_management": {{
                    "program_manager": "required",
                    "steering_committee": "C-level executives",
                    "governance_structure": "defined",
                    "reporting_cadence": "bi-weekly"
                }},
                "project_execution": {{
                    "methodology": "Agile with stage gates",
                    "risk_management": "continuous monitoring",
                    "quality_assurance": "independent validation",
                    "change_management": "structured process"
                }},
                "resource_planning": {{
                    "internal_capacity": "assessment required",
                    "external_vendors": "RFP process",
                    "skill_development": "training program",
                    "knowledge_transfer": "documented process"
                }},
                "success_factors": {{
                    "executive_sponsorship": "critical",
                    "stakeholder_engagement": "continuous",
                    "vendor_management": "strategic partnerships",
                    "technology_adoption": "change management"
                }},
                "monitoring_kpis": {{
                    "schedule_performance": "on-time delivery",
                    "budget_performance": "within budget",
                    "quality_metrics": "zero critical defects",
                    "business_value": "ROI achievement"
                }}
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Implementation plan generated"
            )
        else:
            return response
    
    def _generate_summary(self, request: CapexRequest, capex_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of CAPEX planning results."""
        consolidated = capex_data.get("consolidated_summary", {})
        
        return {
            "total_investment": consolidated.get("total_capex_all_bu", 0),
            "business_units_covered": len(request.business_units),
            "planning_horizon": request.planning_horizon,
            "investment_categories": len(consolidated.get("capex_by_category", {})),
            "expected_roi": consolidated.get("expected_roi", {}).get("average_payback", 0),
            "total_npv": consolidated.get("expected_roi", {}).get("total_npv", 0),
            "high_priority_investments": [
                cat for cat, amount in consolidated.get("capex_by_category", {}).items() 
                if amount > 5000000
            ]
        }
    
    def analyze_technology_trends(self, timeframe: str = "2024-2025") -> AgentResponse:
        """Analyze technology trends for investment planning."""
        prompt = f"""
        Analyze technology trends relevant to telco and fintech CAPEX planning.
        
        Timeframe: {timeframe}
        
        Focus areas:
        - 5G network technology evolution
        - Cloud infrastructure trends
        - AI/ML in telecommunications
        - Fintech technology advances
        - Security and compliance requirements
        - Regulatory technology changes
        
        Provide analysis in JSON format:
        {{
            "technology_trends": {{
                "5g_evolution": {{
                    "current_state": "description",
                    "emerging_technologies": ["Open RAN", "Edge computing"],
                    "investment_implications": "cost and timing considerations",
                    "competitive_advantage": "market positioning impact"
                }},
                "cloud_infrastructure": {{
                    "hybrid_cloud_adoption": "trend analysis",
                    "edge_computing_growth": "market drivers",
                    "cost_optimization": "savings opportunities",
                    "security_considerations": "risk factors"
                }},
                "ai_ml_integration": {{
                    "use_cases": ["network optimization", "customer service"],
                    "technology_maturity": "adoption timeline",
                    "investment_requirements": "budget considerations",
                    "roi_potential": "value creation"
                }}
            }},
            "investment_recommendations": {{
                "immediate_priorities": ["critical investments for next 6 months"],
                "medium_term_opportunities": ["strategic investments for 6-18 months"],
                "long_term_bets": ["emerging technology investments"]
            }}
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Technology trends analysis completed"
            )
        else:
            return response
    
    def score_projects(self, projects: List[Dict[str, Any]], scoring_criteria: Dict[str, str] = None) -> AgentResponse:
        """
        Score CAPEX projects using AI analysis.

        Args:
            projects: List of project dictionaries to score
            scoring_criteria: Dictionary of scoring criteria and descriptions

        Returns:
            AgentResponse with scored projects
        """
        try:
            if not projects:
                return AgentResponse(
                    success=False,
                    error="No projects provided for scoring"
                )

            if scoring_criteria is None:
                scoring_criteria = {
                    "strategic_alignment": "Alignment with business strategy and digital transformation",
                    "roi_potential": "Expected return on investment and financial impact",
                    "risk_level": "Implementation and business risks",
                    "implementation_ease": "Complexity and resource requirements"
                }

            # Use AI to score projects
            scored_projects = []
            for project in projects:
                scoring_result = self._score_single_project(project, scoring_criteria)
                if scoring_result.success:
                    scored_projects.append(scoring_result.data)
                else:
                    # Fallback to basic scoring if AI fails
                    scored_projects.append(self._fallback_project_scoring(project))

            return AgentResponse(
                success=True,
                data={
                    "scored_projects": scored_projects,
                    "scoring_criteria": scoring_criteria,
                    "total_projects": len(scored_projects)
                },
                message=f"Successfully scored {len(scored_projects)} projects"
            )

        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Project scoring failed: {str(e)}"
            )

    def _score_single_project(self, project: Dict[str, Any], scoring_criteria: Dict[str, str]) -> AgentResponse:
        """Score a single project using AI analysis."""
        try:
            prompt = f"""
            Analyze and score this CAPEX project based on the provided criteria.

            Project Details:
            - Name: {project.get('name', 'Unknown')}
            - Budget: ${project.get('budget', 0):,}
            - Category: {project.get('category', 'Unknown')}
            - Priority: {project.get('priority', 'Unknown')}
            - Description: {project.get('description', 'No description provided')}

            Scoring Criteria:
            {json.dumps(scoring_criteria, indent=2)}

            Provide scores (0-100) for each criterion and overall justification.
            Consider:
            - Strategic fit with telco/fintech business model
            - Technology investment best practices
            - Market conditions and competitive landscape
            - Implementation complexity and risks

            Respond in JSON format:
            {{
                "strategic_alignment": 85,
                "roi_potential": 78,
                "risk_level": 35,
                "implementation_ease": 82,
                "ai_score": 77,
                "justification": [
                    "Strategic alignment justification",
                    "ROI potential analysis",
                    "Risk assessment summary"
                ]
            }}
            """

            response = self._call_llm(prompt, json_mode=True)
            if response.success:
                scored_project = {**project, **response.data}
                return AgentResponse(
                    success=True,
                    data=scored_project
                )
            else:
                return AgentResponse(success=False, error="AI scoring failed")

        except Exception as e:
            return AgentResponse(success=False, error=str(e))

    def _fallback_project_scoring(self, project: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback scoring when AI is not available."""
        import random

        # Basic scoring based on project characteristics
        strategic_score = 70
        roi_score = 65
        risk_score = 45
        ease_score = 75

        # Adjust based on category
        category = project.get('category', '').lower()
        if 'infrastructure' in category:
            strategic_score += 15
            risk_score += 10
            ease_score -= 10
        elif 'technology' in category:
            roi_score += 20
            strategic_score += 10
            risk_score += 5
        elif 'analytics' in category:
            roi_score += 25
            strategic_score += 20
            ease_score += 10
        elif 'security' in category:
            strategic_score += 12
            risk_score -= 15  # Lower risk for security
            ease_score += 5

        # Adjust based on priority
        priority = project.get('priority', '').lower()
        if priority == 'high':
            strategic_score += 10
            roi_score += 10
        elif priority == 'low':
            strategic_score -= 5
            roi_score -= 5
            risk_score -= 10

        # Add some variation but keep it realistic
        strategic_score += random.randint(-10, 10)
        roi_score += random.randint(-15, 15)
        risk_score += random.randint(-15, 15)
        ease_score += random.randint(-10, 10)

        # Clamp values
        strategic_score = max(40, min(95, strategic_score))
        roi_score = max(35, min(95, roi_score))
        risk_score = max(20, min(80, risk_score))
        ease_score = max(40, min(95, ease_score))

        ai_score = (strategic_score + roi_score + (100 - risk_score) + ease_score) / 4

        return {
            **project,
            "strategic_alignment": strategic_score,
            "roi_potential": roi_score,
            "risk_level": risk_score,
            "implementation_ease": ease_score,
            "ai_score": round(ai_score),
            "justification": [
                f"Strategic alignment: {strategic_score > 80 and 'Excellent' or strategic_score > 65 and 'Good' or 'Moderate'} fit with business objectives",
                f"ROI potential: {roi_score > 80 and 'High' or roi_score > 60 and 'Medium' or 'Conservative'} expected returns",
                f"Implementation risk: {risk_score < 40 and 'Low' or risk_score < 60 and 'Medium' or 'High'} complexity"
            ]
        }

    def get_capabilities(self) -> Dict[str, Any]:
        """Get the capabilities of the CapexAgent."""
        return {
            "name": self.name,
            "description": "CAPEX planning and technology investment analysis",
            "capabilities": [
                "Technology needs assessment",
                "CAPEX recommendation generation",
                "Investment roadmap creation",
                "ROI and financial analysis",
                "Implementation planning",
                "Technology trend analysis",
                "Risk assessment and mitigation",
                "Resource planning and allocation",
                "Project scoring and prioritization"
            ],
            "supported_categories": list(self.technology_categories.keys()),
            "business_unit_profiles": list(self.bu_tech_profiles.keys()),
            "analysis_types": [
                "Technology Assessment",
                "Investment Prioritization",
                "ROI Analysis",
                "Risk Assessment",
                "Implementation Planning",
                "Project Scoring"
            ],
            "output_formats": ["JSON", "Investment Roadmap", "ROI Dashboard", "Implementation Plan"]
        }