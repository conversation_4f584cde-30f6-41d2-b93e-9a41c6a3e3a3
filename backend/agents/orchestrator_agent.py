"""
OrchestratorAgent - Intelligent workflow orchestration and task delegation.

This agent manages the overall workflow, coordinates between different agents,
and provides conversational interface for user interactions.
"""

import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass

from .base_agent import BaseAgent, AgentResponse
from .fixed_cost_agent import FixedCostAgent
from .macro_data_agent import MacroDataAgent
from .revenue_agent import RevenueAgent
from .scenario_agent import ScenarioAgent
from .consolidation_agent import ConsolidationAgent
from .capex_agent import CapexAgent
from .web_research_agent import WebResearchAgent
from config import get_config

@dataclass
class WorkflowRequest:
    """Request structure for workflow orchestration."""
    task_type: str
    input_data: Any
    parameters: Dict[str, Any] = None
    user_message: str = ""
    session_id: str = ""
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}

class OrchestratorAgent(BaseAgent):
    """
    Central orchestrator agent that manages workflow and agent coordination.
    
    Features:
    - Intelligent task delegation
    - Workflow coordination
    - Conversational interface
    - Session management
    - Error handling and recovery
    - Progress tracking
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__(
            name="OrchestratorAgent",
            role="Workflow Orchestration and Coordination Specialist",
            goal="Manage workflow, coordinate agents, and provide conversational interface",
            backstory="Expert in workflow management with advanced coordination capabilities",
            verbose=verbose
        )
        
        # Initialize specialized agents
        self.agents = {
            "fixed_cost": FixedCostAgent(verbose=verbose),
            "macro_data": MacroDataAgent(verbose=verbose),
            "revenue": RevenueAgent(verbose=verbose),
            "scenario": ScenarioAgent(verbose=verbose),
            "consolidation": ConsolidationAgent(verbose=verbose),
            "capex": CapexAgent(verbose=verbose),
            "web_research": WebResearchAgent(verbose=verbose)
        }
        
        # Session management
        self.sessions = {}
        self.current_session = None
        
        # Workflow state
        self.workflow_history = []
        self.current_workflow = None
        
        # Task type mappings
        self.task_mappings = {
            "process_fixed_costs": "fixed_cost",
            "fetch_macro_data": "macro_data",
            "generate_revenue_forecast": "revenue",
            "create_scenarios": "scenario",
            "generate_scenarios": "scenario",  # Add mapping for generate_scenarios
            "consolidate_data": "consolidation",
            "plan_capex": "capex",
            "conduct_web_research": "web_research",
            "market_intelligence": "web_research",
            "competitor_analysis": "web_research",
            "full_budget_analysis": "orchestrated_workflow",
            "conversational": "orchestrator"  # Handle conversational requests directly
        }
    
    def process(self, input_data: Any, **kwargs) -> AgentResponse:
        """
        Process orchestration request and coordinate workflow.
        
        Args:
            input_data: WorkflowRequest or dict with workflow parameters
            **kwargs: Additional context and parameters
            
        Returns:
            AgentResponse with workflow results
        """
        try:
            # Parse input
            if isinstance(input_data, dict):
                request = WorkflowRequest(**input_data)
            elif isinstance(input_data, WorkflowRequest):
                request = input_data
            else:
                return AgentResponse(
                    success=False,
                    error="Input must be WorkflowRequest or dict"
                )
            
            # Initialize or retrieve session
            session_result = self._manage_session(request)
            if not session_result.success:
                return session_result
            
            # Execute workflow based on task type
            if request.task_type == "full_budget_analysis":
                # Skip intent interpretation for wizard execution to avoid token limits
                workflow_result = self._execute_full_workflow(request, **kwargs)
            else:
                # Interpret user intent for other requests
                intent_result = self._interpret_user_intent(request)
                if not intent_result.success:
                    return intent_result
                workflow_result = self._delegate_to_agent(request, intent_result.data)
            
            # Update session and history
            self._update_workflow_history(request, workflow_result)
            
            return workflow_result
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Orchestration failed: {str(e)}"
            )
    
    def _manage_session(self, request: WorkflowRequest) -> AgentResponse:
        """Manage user session and context."""
        session_id = request.session_id or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if session_id not in self.sessions:
                    self.sessions[session_id] = {
            "created_at": datetime.now().isoformat(),
            "messages": [],
            "workflow_state": {},
            "user_preferences": {},
            "data_cache": {}
        }
        
        self.current_session = session_id
        
        # Add user message to session
        self.sessions[session_id]["messages"].append({
            "timestamp": datetime.now().isoformat(),
            "type": "user",
            "content": request.user_message,
            "task_type": request.task_type
        })
        
        return AgentResponse(
            success=True,
            data={"session_id": session_id},
            message="Session managed successfully"
        )
    
    def _interpret_user_intent(self, request: WorkflowRequest) -> AgentResponse:
        """Interpret user intent using AI."""
        # Skip sanitization for wizard and critical business operations
        if request.task_type in ["full_budget_analysis", "process_fixed_costs", "consolidate_data"]:
            # For these operations, skip intent interpretation entirely for efficiency
            return AgentResponse(
                success=True,
                data={
                    "intent": f"Direct execution of {request.task_type}",
                    "required_agents": [self.task_mappings.get(request.task_type, "unknown")],
                    "workflow_type": "single_agent",
                    "parameters": request.parameters,
                    "suggested_actions": ["Execute the requested task"],
                    "clarifications_needed": [],
                    "estimated_duration": "2-5 minutes",
                    "complexity_level": "medium"
                },
                message="Intent interpretation bypassed for efficiency"
            )
        
        # Sanitize parameters to avoid sending large datasets to LLM
        sanitized_params = self._sanitize_parameters(request.parameters)
        
        prompt = f"""
        Analyze the user's request and determine the appropriate action.
        
        User Message: {request.user_message}
        Task Type: {request.task_type}
        Parameters: {json.dumps(sanitized_params, indent=2)}
        
        Available agents and their capabilities:
        - FixedCostAgent: Excel data processing, cost analysis, data validation
        - MacroDataAgent: External economic data, market analysis, forecasting
        - RevenueAgent: Revenue forecasting, ARPU analysis, churn prediction
        - ScenarioAgent: Scenario generation, Monte Carlo simulation, risk analysis
        - ConsolidationAgent: Financial consolidation, reporting, executive insights
        - CapexAgent: Technology investment planning, CAPEX analysis, ROI calculations
        - WebResearchAgent: Market intelligence, competitor analysis, industry trends, regulatory monitoring
        
        Session Context: {json.dumps(self.sessions.get(self.current_session, {}).get("workflow_state", {}), indent=2)}
        
        Determine the user's intent and respond with JSON:
        {{
            "intent": "primary intent of the user",
            "required_agents": ["list of agents needed"],
            "workflow_type": "single_agent or multi_agent or conversational",
            "parameters": {{"refined parameters based on intent"}},
            "suggested_actions": ["suggested actions for the user"],
            "clarifications_needed": ["any clarifications needed from user"],
            "estimated_duration": "estimated time to complete",
            "complexity_level": "low/medium/high"
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="User intent interpreted successfully"
            )
        else:
            return response
    
    def _execute_full_workflow(self, request: WorkflowRequest, **kwargs) -> AgentResponse:
        """Execute full budget analysis workflow."""
        try:
            workflow_results = {
                "workflow_id": f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "steps": [],
                "results": {},
                "summary": {}
            }
            
            # Step 1: Process fixed costs
            if "fixed_cost_data" in request.parameters:
                fixed_cost_result = self.agents["fixed_cost"].process(
                    request.parameters["fixed_cost_data"]
                )
                workflow_results["steps"].append({
                    "step": "fixed_costs",
                    "status": "completed" if fixed_cost_result.success else "failed",
                    "duration": "estimated",
                    "agent": "FixedCostAgent"
                })
                if fixed_cost_result.success:
                    workflow_results["results"]["fixed_costs"] = fixed_cost_result.data
            
            # Step 2: Fetch macro data
            if "macro_data_request" in request.parameters:
                macro_result = self.agents["macro_data"].process(
                    request.parameters["macro_data_request"]
                )
                workflow_results["steps"].append({
                    "step": "macro_data",
                    "status": "completed" if macro_result.success else "failed",
                    "duration": "estimated",
                    "agent": "MacroDataAgent"
                })
                if macro_result.success:
                    workflow_results["results"]["macro"] = macro_result.data
            
            # Step 3: Generate revenue forecasts
            if "revenue_request" in request.parameters:
                revenue_result = self.agents["revenue"].process(
                    request.parameters["revenue_request"]
                )
                workflow_results["steps"].append({
                    "step": "revenue_forecast",
                    "status": "completed" if revenue_result.success else "failed",
                    "duration": "estimated",
                    "agent": "RevenueAgent"
                })
                if revenue_result.success:
                    workflow_results["results"]["revenue"] = revenue_result.data
            
            # Step 3.5: Plan CAPEX investments
            if "capex_data" in request.parameters or "capex_request" in request.parameters:
                # Handle both wizard format (capex_data) and legacy format (capex_request)
                capex_input = request.parameters.get("capex_data") or request.parameters.get("capex_request")

                capex_result = self.agents["capex"].process(
                    capex_input,
                    market_data=workflow_results["results"].get("macro", {})
                )
                workflow_results["steps"].append({
                    "step": "capex_planning",
                    "status": "completed" if capex_result.success else "failed",
                    "duration": "estimated",
                    "agent": "CapexAgent"
                })
                if capex_result.success:
                    workflow_results["results"]["capex"] = capex_result.data
            
            # Step 3.6: Conduct web research for market intelligence
            if "web_research_request" in request.parameters:
                web_research_result = self.agents["web_research"].process(
                    request.parameters["web_research_request"],
                    market_data=workflow_results["results"].get("macro", {}),
                    business_context=workflow_results["results"]
                )
                workflow_results["steps"].append({
                    "step": "web_research",
                    "status": "completed" if web_research_result.success else "failed",
                    "duration": "estimated",
                    "agent": "WebResearchAgent"
                })
                if web_research_result.success:
                    workflow_results["results"]["web_research"] = web_research_result.data
            
            # Step 4: Generate scenarios
            if "scenario_request" in request.parameters:
                scenario_result = self.agents["scenario"].process(
                    request.parameters["scenario_request"],
                    historical_data=workflow_results["results"].get("revenue", {}),
                    market_data=workflow_results["results"].get("macro", {})
                )
                workflow_results["steps"].append({
                    "step": "scenario_generation",
                    "status": "completed" if scenario_result.success else "failed",
                    "duration": "estimated",
                    "agent": "ScenarioAgent"
                })
                if scenario_result.success:
                    workflow_results["results"]["scenarios"] = scenario_result.data
            
            # Step 5: Consolidate all data
            consolidation_input = {
                "fixed_costs": workflow_results["results"].get("fixed_costs", {}),
                "revenue": workflow_results["results"].get("revenue", {}),
                "macro": workflow_results["results"].get("macro", {}),
                "scenarios": workflow_results["results"].get("scenarios", {}),
                "capex": workflow_results["results"].get("capex", {}),
                "web_research": workflow_results["results"].get("web_research", {})
            }
            
            consolidation_result = self.agents["consolidation"].process(consolidation_input)
            workflow_results["steps"].append({
                "step": "consolidation",
                "status": "completed" if consolidation_result.success else "failed",
                "duration": "estimated",
                "agent": "ConsolidationAgent"
            })
            if consolidation_result.success:
                workflow_results["results"]["consolidated"] = consolidation_result.data
            
            # Generate workflow summary
            workflow_results["summary"] = self._generate_workflow_summary(workflow_results)
            
            return AgentResponse(
                success=True,
                data=workflow_results,
                message="Full budget analysis workflow completed"
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Full workflow execution failed: {str(e)}"
            )
    
    def _delegate_to_agent(self, request: WorkflowRequest, intent_data: Dict[str, Any]) -> AgentResponse:
        """Delegate task to appropriate agent."""
        agent_name = self.task_mappings.get(request.task_type)
        
        if not agent_name:
            return AgentResponse(
                success=False,
                error=f"No agent available for task type: {request.task_type}"
            )
        
        # Handle conversational requests directly
        if agent_name == "orchestrator":
            return self._generate_conversational_response(request.user_message, intent_data)
        
        # Handle orchestrated workflow
        if agent_name == "orchestrated_workflow":
            # For wizard execution, we can skip intent interpretation and go directly to workflow
            if request.task_type == "full_budget_analysis":
                return self._execute_full_workflow(request)
            else:
                return self._execute_full_workflow(request)
        
        # Check if agent exists
        if agent_name not in self.agents:
            return AgentResponse(
                success=False,
                error=f"Agent {agent_name} not found"
            )
        
        agent = self.agents[agent_name]
        
        # Prepare input for agent
        agent_input = request.input_data
        if intent_data.get("parameters"):
            if isinstance(agent_input, dict):
                agent_input.update(intent_data["parameters"])
            else:
                agent_input = intent_data["parameters"]
        
        # Execute agent task
        result = agent.process(agent_input)
        
        return AgentResponse(
            success=result.success,
            data={
                "agent_result": result.data,
                "agent_name": agent_name,
                "intent_analysis": intent_data,
                "execution_time": datetime.now().isoformat()
            },
            error=result.error,
            message=f"Task delegated to {agent_name}: {result.message}"
        )
    
    def _generate_workflow_summary(self, workflow_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of workflow execution."""
        completed_steps = len([s for s in workflow_results["steps"] if s["status"] == "completed"])
        total_steps = len(workflow_results["steps"])
        
        summary = {
            "completion_rate": completed_steps / total_steps * 100 if total_steps > 0 else 0,
            "completed_steps": completed_steps,
            "total_steps": total_steps,
            "failed_steps": [s["step"] for s in workflow_results["steps"] if s["status"] == "failed"],
            "success_rate": completed_steps / total_steps * 100 if total_steps > 0 else 0,
            "data_quality": self._assess_data_quality(workflow_results["results"]),
            "recommendations": self._generate_workflow_recommendations(workflow_results)
        }
        
        return summary
    
    def _assess_data_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall data quality across workflow results."""
        quality_assessment = {
            "overall_score": 0.0,
            "component_scores": {},
            "issues": [],
            "recommendations": []
        }
        
        scores = []
        
        # Assess fixed costs data quality
        if "fixed_costs" in results:
            fixed_cost_quality = results["fixed_costs"].get("summary", {}).get("data_quality_score", 0.8)
            quality_assessment["component_scores"]["fixed_costs"] = fixed_cost_quality
            scores.append(fixed_cost_quality)
        
        # Assess other components
        for component in ["revenue", "macro", "scenarios"]:
            if component in results:
                # Simplified quality assessment
                component_score = 0.85  # Default good quality
                quality_assessment["component_scores"][component] = component_score
                scores.append(component_score)
        
        # Calculate overall score
        if scores:
            quality_assessment["overall_score"] = sum(scores) / len(scores)
        
        return quality_assessment
    
    def _generate_workflow_recommendations(self, workflow_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on workflow results."""
        recommendations = []
        
        # Check for failed steps
        failed_steps = [s for s in workflow_results["steps"] if s["status"] == "failed"]
        if failed_steps:
            recommendations.append(f"Review and retry failed steps: {[s['step'] for s in failed_steps]}")
        
        # Check data quality
        results = workflow_results.get("results", {})
        if "fixed_costs" in results:
            data_quality = results["fixed_costs"].get("summary", {}).get("data_quality_score", 1.0)
            if data_quality < 0.8:
                recommendations.append("Improve fixed cost data quality before proceeding")
        
        # General recommendations
        if len(workflow_results["steps"]) < 5:
            recommendations.append("Consider running full budget analysis for comprehensive results")
        
        return recommendations
    
    def _update_workflow_history(self, request: WorkflowRequest, result: AgentResponse) -> None:
        """Update workflow history and session state."""
        history_entry = {
            "timestamp": datetime.now().isoformat(),
            "request": {
                "task_type": request.task_type,
                "user_message": request.user_message,
                "parameters": request.parameters
            },
            "result": {
                "success": result.success,
                "message": result.message,
                "error": result.error
            },
            "session_id": request.session_id
        }
        
        self.workflow_history.append(history_entry)
        
        # Update session state
        if self.current_session and self.current_session in self.sessions:
            self.sessions[self.current_session]["workflow_state"]["last_request"] = request.task_type
            self.sessions[self.current_session]["workflow_state"]["last_result"] = result.success
            
            # Add response to session messages
            self.sessions[self.current_session]["messages"].append({
                "timestamp": datetime.now().isoformat(),
                "type": "assistant",
                "content": result.message,
                "success": result.success
            })
    
    def get_session_context(self, session_id: str) -> AgentResponse:
        """Get context for a specific session."""
        if session_id not in self.sessions:
            return AgentResponse(
                success=False,
                error=f"Session {session_id} not found"
            )
        
        session_data = self.sessions[session_id]
        return AgentResponse(
            success=True,
            data=session_data,
            message=f"Session context retrieved for {session_id}"
        )
    
    def get_workflow_status(self) -> AgentResponse:
        """Get current workflow status."""
        status = {
            "active_sessions": len(self.sessions),
            "total_workflows": len(self.workflow_history),
            "recent_workflows": self.workflow_history[-5:] if self.workflow_history else [],
            "agent_status": {
                name: agent.get_agent_info() for name, agent in self.agents.items()
            }
        }
        
        return AgentResponse(
            success=True,
            data=status,
            message="Workflow status retrieved successfully"
        )
    
    def chat_interface(self, user_message: str, session_id: str = None) -> AgentResponse:
        """Conversational interface for natural language interactions."""
        # Create workflow request from chat message
        request = WorkflowRequest(
            task_type="conversational",
            input_data={"message": user_message},
            user_message=user_message,
            session_id=session_id or ""
        )
        
        # Interpret intent and generate response
        intent_result = self._interpret_user_intent(request)
        if not intent_result.success:
            return intent_result
        
        # Generate conversational response
        response_result = self._generate_conversational_response(user_message, intent_result.data)
        
        # Update session
        self._update_workflow_history(request, response_result)
        
        return response_result
    
    def _sanitize_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize parameters to avoid sending large datasets to LLM while preserving business context."""
        if not parameters:
            return {}
        
        sanitized = {}
        for key, value in parameters.items():
            # Keep small datasets intact for business intelligence
            if isinstance(value, list):
                if len(value) > 100:  # Increased threshold for business data
                    # For business data, provide more meaningful summaries
                    if value and isinstance(value[0], dict):
                        # Business records - show structure and sample
                        sample_keys = list(value[0].keys()) if value[0] else []
                        sanitized[key] = f"[{len(value)} business records] - Structure: {sample_keys[:8]}..."
                    else:
                        sanitized[key] = f"[{len(value)} items] - Type: {type(value[0]).__name__ if value else 'empty'}"
                else:
                    sanitized[key] = value  # Keep smaller lists intact
            elif isinstance(value, dict):
                if len(str(value)) > 2000:  # Increased threshold for business context
                    # For business data, preserve key structure information
                    key_info = {k: f"<{type(v).__name__}>" for k, v in list(value.items())[:10]}
                    sanitized[key] = f"{{dict with {len(value)} keys}} - Sample: {key_info}"
                else:
                    sanitized[key] = value  # Keep smaller dicts intact
            elif isinstance(value, str) and len(value) > 1000:  # Increased threshold
                # For business text, preserve beginning and end
                sanitized[key] = value[:700] + "...[truncated]..." + value[-200:]
            else:
                sanitized[key] = value
        
        return sanitized

    def _generate_conversational_response(self, user_message: str, intent_data: Dict[str, Any]) -> AgentResponse:
        """Generate conversational response based on user intent."""
        prompt = f"""
        You are OneOptimizer, an AI assistant for budget analysis and financial planning.
        
        User Message: {user_message}
        
        Intent Analysis: {json.dumps(intent_data, indent=2)}
        
        Available capabilities:
        - Process Excel files with cost data
        - Fetch macro-economic data
        - Generate revenue forecasts
        - Create business scenarios
        - Plan CAPEX investments
        - Conduct market research and competitor analysis
        - Consolidate financial data
        - Generate executive reports
        
        Respond conversationally as OneOptimizer in JSON format:
        {{
            "response": "conversational response to the user",
            "suggested_actions": ["specific actions the user can take"],
            "required_inputs": ["any inputs needed from the user"],
            "next_steps": ["recommended next steps"],
            "help_available": ["additional help options"]
        }}
        """
        
        response = self._call_llm(prompt, json_mode=True)
        if response.success:
            return AgentResponse(
                success=True,
                data=response.data,
                message="Conversational response generated"
            )
        else:
            return AgentResponse(
                success=True,
                data={
                    "response": "I'm here to help with your budget analysis. How can I assist you today?",
                    "suggested_actions": ["Upload cost data", "Request revenue forecast", "Generate scenarios"],
                    "required_inputs": [],
                    "next_steps": ["Tell me what you'd like to work on"],
                    "help_available": ["Data processing", "Financial analysis", "Report generation"]
                },
                message="Default conversational response"
            )
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get the capabilities of the OrchestratorAgent."""
        agent_capabilities = {}
        for name, agent in self.agents.items():
            agent_capabilities[name] = agent.get_capabilities()
        
        return {
            "name": self.name,
            "description": "Workflow orchestration and agent coordination",
            "capabilities": [
                "Intelligent task delegation",
                "Workflow coordination",
                "Conversational interface",
                "Session management",
                "Multi-agent orchestration",
                "Error handling and recovery",
                "Progress tracking",
                "Intent interpretation"
            ],
            "available_agents": list(self.agents.keys()),
            "agent_capabilities": agent_capabilities,
            "supported_workflows": [
                "Full budget analysis",
                "Cost data processing",
                "Revenue forecasting",
                "Scenario generation",
                "CAPEX planning",
                "Market intelligence research",
                "Competitor analysis",
                "Financial consolidation",
                "Conversational assistance"
            ],
            "task_types": list(self.task_mappings.keys())
        }